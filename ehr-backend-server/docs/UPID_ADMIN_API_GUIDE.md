# UPID Configuration Admin API Guide

## Overview

The UPID Configuration Admin API provides comprehensive administrative capabilities for managing Unique Patient ID (UPID) formats dynamically. This system allows administrators to modify UPID configuration through a web interface without requiring application restarts.

## Features

✅ **Dynamic Configuration Updates** - Change UPID formats without restarting the application  
✅ **Configuration Validation** - Validate changes before applying them  
✅ **Configuration Preview** - Preview sample UPIDs with new configurations  
✅ **Rollback Capability** - Rollback to previous configurations  
✅ **Change History** - Track all configuration changes with audit trail  
✅ **Swagger Documentation** - Fully documented APIs in Swagger UI  
✅ **Backward Compatibility** - Existing patient IDs remain unchanged  

## API Endpoints

### 📋 **Read-Only Endpoints**

#### Get Current Configuration
```http
GET /api/upid-config/current
```
**Description**: Retrieves the current effective UPID configuration  
**Response**: Complete configuration including format, sequence, and validation settings

#### Get Available Formats
```http
GET /api/upid-config/formats
```
**Description**: Lists all available UPID format types with examples and patterns  
**Response**: Array of format information with descriptions and validation patterns

#### Generate Sample UPID
```http
POST /api/upid-config/generate-sample/{facilityId}
```
**Description**: Generates a sample UPID using current configuration  
**Parameters**: `facilityId` - Facility ID for sample generation

#### Validate UPID
```http
POST /api/upid-config/validate
Content-Type: application/json

{
  "upid": "001-00-0000-0001"
}
```
**Description**: Validates whether a UPID conforms to current format

#### Health Check
```http
GET /api/upid-config/health
```
**Description**: Performs health check on UPID configuration system

### 🔧 **Admin Endpoints**

#### Get Effective Configuration
```http
GET /api/upid-config/admin/effective
```
**Description**: Retrieves complete effective configuration (static + dynamic)  
**Response**: Key-value pairs of all configuration settings

#### Validate Configuration Changes
```http
POST /api/upid-config/admin/validate
Content-Type: application/json

{
  "changes": {
    "upid.format.type": "CUSTOM",
    "upid.format.custom.template": "HSP{FACILITY}{YEAR}{SEQUENCE:6}"
  },
  "validateOnly": true
}
```
**Description**: Validates proposed configuration changes without applying them  
**Response**: Validation result with errors, warnings, and sample UPIDs

#### Preview Configuration Changes
```http
POST /api/upid-config/admin/preview
Content-Type: application/json

{
  "upid.format.type": "NUMERIC",
  "upid.format.numeric.prefix": "2",
  "upid.format.numeric.total-digits": "12"
}
```
**Description**: Previews impact of configuration changes with sample UPIDs  
**Response**: Validation results, current vs new config, and sample UPIDs

#### Apply Configuration Changes
```http
PUT /api/upid-config/admin/apply
Content-Type: application/json

{
  "changes": {
    "upid.format.standard.facility-digits": "4",
    "upid.format.standard.separator": "_"
  },
  "changeReason": "Updated for new facility numbering system",
  "changedBy": "<EMAIL>"
}
```
**Description**: Applies validated configuration changes to the system  
**Response**: Validation result and updated configuration

#### Rollback Configuration
```http
POST /api/upid-config/admin/rollback
Content-Type: application/json

{
  "configKey": "upid.format.type",
  "rollbackReason": "Reverting due to compatibility issues",
  "rolledBackBy": "<EMAIL>"
}
```
**Description**: Rolls back a configuration setting to previous value  
**Response**: Success status and rollback confirmation

#### Get Configuration History
```http
GET /api/upid-config/admin/history?configKey=upid.format.type
```
**Description**: Retrieves history of configuration changes  
**Parameters**: `configKey` (optional) - Specific configuration key to get history for  
**Response**: Array of historical changes with timestamps and user information

#### Generate Test Samples
```http
POST /api/upid-config/admin/test-samples/{facilityId}
Content-Type: application/json

{
  "upid.format.type": "CUSTOM",
  "upid.format.custom.template": "TEST{FACILITY}{SEQUENCE:4}"
}
```
**Description**: Generates sample UPIDs using temporary configuration for testing  
**Response**: Validation results and sample UPIDs with test configuration

## Configuration Keys

### Format Configuration
- `upid.format.type` - Format type (STANDARD, CUSTOM, NUMERIC, ALPHANUMERIC, UUID)

### Standard Format
- `upid.format.standard.facility-digits` - Number of facility digits (1-5)
- `upid.format.standard.network-digits` - Number of network digits (1-3)
- `upid.format.standard.sequence-digits` - Number of sequence digits (4-12)
- `upid.format.standard.separator` - Separator character
- `upid.format.standard.network-id` - Default network ID

### Custom Format
- `upid.format.custom.template` - Template with placeholders

### Numeric Format
- `upid.format.numeric.prefix` - Numeric prefix
- `upid.format.numeric.total-digits` - Total digits (5-20)

### Alphanumeric Format
- `upid.format.alphanumeric.prefix` - Alphanumeric prefix
- `upid.format.alphanumeric.facility-digits` - Facility digits (1-5)
- `upid.format.alphanumeric.suffix-pattern` - Suffix pattern

### UUID Format
- `upid.format.uuid.prefix` - UUID prefix
- `upid.format.uuid.include-facility` - Include facility in UUID

### Sequence Configuration
- `upid.sequence.global` - Use global sequence
- `upid.sequence.start-from` - Starting sequence number
- `upid.sequence.reset-annually` - Reset sequence annually
- `upid.sequence.reset-monthly` - Reset sequence monthly

### Validation Configuration
- `upid.validation.check-duplicates` - Check for duplicates
- `upid.validation.allow-manual-override` - Allow manual override
- `upid.validation.reserved-patterns` - Reserved patterns (comma-separated)

## Usage Examples

### Example 1: Change to Custom Format
```bash
# 1. Preview the change
curl -X POST http://localhost:8080/api/upid-config/admin/preview \
  -H "Content-Type: application/json" \
  -d '{
    "upid.format.type": "CUSTOM",
    "upid.format.custom.template": "HSP{FACILITY}{YEAR}{SEQUENCE:6}"
  }'

# 2. Apply the change
curl -X PUT http://localhost:8080/api/upid-config/admin/apply \
  -H "Content-Type: application/json" \
  -d '{
    "changes": {
      "upid.format.type": "CUSTOM",
      "upid.format.custom.template": "HSP{FACILITY}{YEAR}{SEQUENCE:6}"
    },
    "changeReason": "Switching to hospital-specific format",
    "changedBy": "<EMAIL>"
  }'
```

### Example 2: Modify Standard Format
```bash
# Change facility digits and separator
curl -X PUT http://localhost:8080/api/upid-config/admin/apply \
  -H "Content-Type: application/json" \
  -d '{
    "changes": {
      "upid.format.standard.facility-digits": "4",
      "upid.format.standard.separator": "_"
    },
    "changeReason": "Updated for 4-digit facility codes",
    "changedBy": "<EMAIL>"
  }'
```

### Example 3: Rollback Configuration
```bash
# Rollback format type to previous value
curl -X POST http://localhost:8080/api/upid-config/admin/rollback \
  -H "Content-Type: application/json" \
  -d '{
    "configKey": "upid.format.type",
    "rollbackReason": "Reverting due to integration issues",
    "rolledBackBy": "<EMAIL>"
  }'
```

## Swagger UI Access

The complete API documentation is available in Swagger UI:

**URL**: `http://localhost:8080/q/swagger-ui`

**Features**:
- Interactive API testing
- Request/response examples
- Schema documentation
- Error code explanations
- Try-it-out functionality

## Security Considerations

### Authentication
- Admin endpoints should be protected with appropriate authentication
- Consider implementing role-based access control (RBAC)
- Use HTTPS in production environments

### Validation
- All configuration changes are validated before application
- Invalid configurations are rejected with detailed error messages
- Rollback capability provides safety net for problematic changes

### Audit Trail
- All configuration changes are logged with timestamps
- User information is tracked for accountability
- Change reasons are recorded for documentation

## Best Practices

### Before Making Changes
1. **Preview First**: Always use preview endpoint before applying changes
2. **Validate**: Use validation endpoint to check for errors
3. **Test Samples**: Generate test samples to verify format
4. **Backup**: Note current configuration for potential rollback

### During Changes
1. **Small Changes**: Make incremental changes rather than large overhauls
2. **Document Reasons**: Always provide clear change reasons
3. **User Tracking**: Include user information for accountability

### After Changes
1. **Verify**: Generate sample UPIDs to confirm changes
2. **Monitor**: Watch for any issues with new patient registrations
3. **Document**: Update organizational documentation

## Troubleshooting

### Common Issues
1. **Validation Errors**: Check configuration key names and value formats
2. **Rollback Failures**: Ensure rollback target exists in history
3. **Sample Generation**: Verify facility ID format and configuration validity

### Error Responses
- **400 Bad Request**: Invalid configuration or validation failure
- **404 Not Found**: Configuration key or rollback target not found
- **500 Internal Server Error**: System error during configuration update

### Support
- Check application logs for detailed error information
- Use health check endpoint to verify system status
- Review configuration history for recent changes
