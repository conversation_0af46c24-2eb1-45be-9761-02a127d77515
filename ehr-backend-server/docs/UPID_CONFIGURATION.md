# UPID (Unique Patient ID) Configuration Guide

## Overview

The EHR backend system supports configurable Unique Patient ID (UPID) formats to enable healthcare organizations to customize patient identification standards according to their specific requirements. This system maintains backward compatibility with existing patient IDs while allowing new patients to use the configured UPID format.

## Configuration Properties

All UPID configuration is managed through the `application.properties` file under the `upid.*` prefix.

### Format Type Selection

```properties
# Available types: STAN<PERSON>RD, CUSTOM, NUMERIC, ALPHANUMERIC, UUID
upid.format.type=STANDARD
```

## Supported UPID Formats

### 1. STANDARD Format (Default)

**Pattern**: `001-00-0000-0001`
**Description**: Maintains backward compatibility with existing MRN format

```properties
upid.format.standard.facility-digits=3
upid.format.standard.network-digits=2
upid.format.standard.sequence-digits=8
upid.format.standard.separator=-
upid.format.standard.network-id=00
```

**Example**: `001-00-0000-0001`, `002-00-0000-0002`, `001-00-0000-0003`

### 2. CUSTOM Format

**Pattern**: Configurable template with placeholders
**Description**: Flexible format using template placeholders

```properties
upid.format.custom.template=PAT{FACILITY}{YEAR}{SEQUENCE:6}
```

**Available Placeholders**:
- `{FACILITY}` - Facility ID (padded to 3 digits)
- `{NETWORK}` - Network ID from standard config
- `{YEAR}` - Current year (4 digits)
- `{MONTH}` - Current month (2 digits)
- `{DAY}` - Current day (2 digits)
- `{SEQUENCE:n}` - Sequential number padded to n digits

**Example**: `PAT00120240001`, `PAT00220240002`

### 3. NUMERIC Format

**Pattern**: Pure numeric format
**Description**: Simple numeric IDs with configurable prefix and total digits

```properties
upid.format.numeric.prefix=1
upid.format.numeric.total-digits=10
```

**Example**: `1000000001`, `1000000002`, `1000000003`

### 4. ALPHANUMERIC Format

**Pattern**: Alphanumeric with prefix and suffix
**Description**: Combines letters and numbers for enhanced readability

```properties
upid.format.alphanumeric.prefix=P
upid.format.alphanumeric.facility-digits=3
upid.format.alphanumeric.suffix-pattern=A{SEQUENCE:4}
```

**Example**: `P001A0001`, `P002A0002`, `P001A0003`

### 5. UUID Format

**Pattern**: UUID-based format
**Description**: Maximum uniqueness using UUID with optional facility inclusion

```properties
upid.format.uuid.prefix=PAT-
upid.format.uuid.include-facility=true
```

**Example**: `PAT-F1-550e8400-e29b-41d4-a716-************`

## Sequence Management

### Global vs Facility-Specific Sequences

```properties
# Global sequence (continuous across all facilities)
upid.sequence.global=true

# Starting number for sequences
upid.sequence.start-from=1

# Reset options (not recommended for production)
upid.sequence.reset-annually=false
upid.sequence.reset-monthly=false
```

**Global Sequence**: Ensures continuous numbering across all facilities (recommended)
**Facility-Specific**: Each facility maintains its own sequence counter

## Validation Configuration

```properties
# Check for duplicate UPIDs before generation
upid.validation.check-duplicates=true

# Allow manual override of generated UPIDs (admin feature)
upid.validation.allow-manual-override=false

# Reserved patterns that cannot be used
upid.validation.reserved-patterns=ADMIN,TEST,DEMO
```

## Migration and Backward Compatibility

### Existing Patient IDs

- All existing patient IDs remain unchanged
- The system continues to support the existing format for lookups and operations
- New patients will use the configured UPID format

### Migration Strategy

1. **Phase 1**: Configure UPID format while keeping existing IDs
2. **Phase 2**: All new registrations use the new format
3. **Phase 3**: Optional migration of existing IDs (requires careful planning)

## API Endpoints

### Configuration Management

- `GET /api/upid/config` - Get current UPID configuration
- `GET /api/upid/formats` - List all available format types
- `POST /api/upid/sample` - Generate sample UPID for testing
- `POST /api/upid/validate` - Validate UPID format and uniqueness

### Patient Registration

The patient registration API automatically uses the configured UPID format:

```json
POST /api/patients
{
  "facilityId": "1",
  "firstName": "John",
  "lastName": "Doe",
  // ... other patient data
}
```

Response includes the generated UPID:
```json
{
  "patientId": "001-00-0000-0001",
  "facilityId": "1",
  // ... other patient data
}
```

## Best Practices

### Production Recommendations

1. **Use STANDARD format** for maximum compatibility
2. **Enable global sequences** for continuous numbering
3. **Enable duplicate checking** for data integrity
4. **Disable manual override** unless specifically needed
5. **Test configuration** thoroughly before production deployment

### Format Selection Guidelines

- **STANDARD**: Best for organizations migrating from existing MRN systems
- **CUSTOM**: Ideal for organizations with specific formatting requirements
- **NUMERIC**: Suitable for simple, numeric-only requirements
- **ALPHANUMERIC**: Good balance of readability and uniqueness
- **UUID**: Maximum uniqueness for large-scale deployments

### Security Considerations

- UPIDs should not contain sensitive information
- Consider format predictability vs security requirements
- Reserved patterns help prevent conflicts with system accounts

## Troubleshooting

### Common Issues

1. **Duplicate UPID Generation**: Enable `upid.validation.check-duplicates=true`
2. **Format Validation Errors**: Check template syntax and placeholder usage
3. **Sequence Gaps**: Normal behavior when validation fails; sequences continue
4. **Migration Issues**: Ensure backward compatibility settings are correct

### Monitoring

- Monitor UPID generation performance
- Track duplicate detection rates
- Verify sequence continuity
- Check format compliance

## Examples

### Healthcare Network Configuration

```properties
# Multi-facility healthcare network
upid.format.type=STANDARD
upid.format.standard.facility-digits=3
upid.format.standard.network-id=01
upid.sequence.global=true
```

### Clinic Chain Configuration

```properties
# Clinic chain with year-based IDs
upid.format.type=CUSTOM
upid.format.custom.template=CLI{FACILITY}{YEAR}{SEQUENCE:5}
upid.sequence.global=false
```

### Hospital System Configuration

```properties
# Large hospital system with UUID
upid.format.type=UUID
upid.format.uuid.prefix=HSP-
upid.format.uuid.include-facility=true
upid.sequence.global=true
```
