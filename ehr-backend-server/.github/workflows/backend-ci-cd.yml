name: Java 21 Quarkus CI/CD (Maven)

on:
  push:
    branches: [ Main ]
  pull_request:
    branches: [ Main ]
  workflow_dispatch:

jobs:
  # ──────────────────── 1. Compile & Test ────────────────────
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: 21
          cache: maven

      - name: Build & run tests with Maven
        run: mvn -B clean test

  # ──────────────── 2. Build & Push Docker Image ────────────────
  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: 21
          cache: maven

      - name: Build Quarkus fast-jar
        run: mvn -B package -DskipTests -Dquarkus.package.type=fast-jar

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          registry: docker.io
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build & push image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: src/main/docker/Dockerfile.jvm
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/quarkus-backend:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/quarkus-backend:${{ github.sha }}


  # ──────────────── 3. Deploy on Self-Hosted Windows ────────────────
  deploy:
    needs: build-and-push
    runs-on: [ self-hosted, windows ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy container locally
        shell: powershell
        run: |
          # Pull the latest image
          docker pull ${{ secrets.DOCKERHUB_USERNAME }}/quarkus-backend:latest

          # Remove any existing container (stop + remove) if it exists
          try {
            docker rm -f quarkus-backend
          } catch {
            Write-Host "No existing 'quarkus-backend' container to remove."
          }

          # Run the new container
          docker run -d --name quarkus-backend -p 8080:8080 ${{ secrets.DOCKERHUB_USERNAME }}/quarkus-backend:latest
