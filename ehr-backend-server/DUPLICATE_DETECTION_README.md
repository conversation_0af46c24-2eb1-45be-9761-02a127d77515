# Automated Duplicate Patient Detection System

## Overview

The EHR system now includes a comprehensive automated duplicate patient detection system that performs real-time validation during patient registration. The system uses configurable matching criteria and weighted scoring algorithms to identify potential duplicate patients.

## Features

### 1. Real-time Duplicate Detection
- Automatically checks for duplicates during patient registration
- Configurable matching criteria with weighted scoring
- Performance optimized (completes within 2 seconds)
- Timeout protection to prevent registration delays

### 2. Matching Algorithms
- **Exact Matching**: Perfect matches on critical fields
- **Fuzzy Matching**: Handles typos and variations using Jaro-Winkler similarity
- **Phonetic Matching**: Soundex-like algorithm for name variations
- **Multi-field Scoring**: Weighted combination of multiple criteria

### 3. Matching Criteria
- **Name Matching**: First, middle, and last names with fuzzy matching
- **Date of Birth**: Exact match required for high confidence
- **Contact Information**: Phone numbers and email addresses
- **Address Information**: Street, city, state, zip code with fuzzy matching
- **National Identifiers**: ABHA, Aadhaar, and other ID numbers

### 4. Confidence Levels and Actions
- **High Confidence (≥85%)**: Blocks registration, requires manual review
- **Medium Confidence (70-84%)**: Flags for review, allows registration
- **Low Confidence (<70%)**: Allows registration with minimal logging

### 5. Duplicate Resolution Workflow
- **Merge Records**: Combine duplicate patient records
- **Mark as Different**: Flag as false positive
- **Create New Anyway**: Override duplicate detection

## API Endpoints

### Patient Registration
```
POST /api/patients
```
- Enhanced with automatic duplicate detection
- Returns HTTP 409 (Conflict) if high-confidence duplicate detected
- Includes duplicate information in error response

### Duplicate Detection Management
```
POST /api/patients/duplicates/check
GET  /api/patients/duplicates/pending
POST /api/patients/duplicates/resolve
POST /api/patients/duplicates/batch
GET  /api/patients/duplicates/statistics
GET  /api/patients/duplicates/relationships/{patientId}
```

### Configuration Management
```
GET  /api/admin/duplicate-detection/config
PUT  /api/admin/duplicate-detection/config
POST /api/admin/duplicate-detection/config/reset
```

## Configuration

### Application Properties
```properties
# Duplicate Detection Configuration
duplicate.detection.enabled=true
duplicate.detection.threshold.high=85
duplicate.detection.threshold.medium=70
duplicate.detection.timeout.seconds=2
duplicate.detection.audit.enabled=true
duplicate.detection.batch.enabled=true
```

### Matching Weights (Default Values)
- **Name Exact Match**: 40 points
- **Name Fuzzy Match**: 25 points
- **Date of Birth**: 30 points
- **Phone Number**: 20 points
- **Email Address**: 15 points
- **Address**: 10 points
- **National Identifier**: 35 points

### Database Configuration
The system uses three main tables:
- `duplicate_detection_config`: Stores configuration settings
- `duplicate_detection_logs`: Audit trail of all detection events
- `duplicate_patient_relationships`: Tracks confirmed duplicates and false positives

## Usage Examples

### 1. Manual Duplicate Check
```bash
curl -X POST http://localhost:8080/api/patients/duplicates/check \
  -H "Content-Type: application/json" \
  -d '{
    "facilityId": "1",
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "identifierType": "ABHA",
    "identifierNumber": "12-3456-7890-1234"
  }'
```

### 2. Resolve Duplicate
```bash
curl -X POST http://localhost:8080/api/patients/duplicates/resolve \
  -H "Content-Type: application/json" \
  -d '{
    "candidatePatientId": "001-00-0000-0001",
    "duplicatePatientId": "002-00-0000-0001",
    "action": "MERGE_RECORDS",
    "reviewedBy": "admin",
    "notes": "Confirmed duplicate - same person"
  }'
```

### 3. Update Configuration
```bash
curl -X PUT http://localhost:8080/api/admin/duplicate-detection/config \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "highThreshold": 90,
    "mediumThreshold": 75,
    "weights": {
      "nameExact": 45,
      "nameFuzzy": 30,
      "dateOfBirth": 35,
      "phone": 25,
      "email": 20,
      "address": 15,
      "identifier": 40
    },
    "fuzzyThreshold": 85,
    "timeoutSeconds": 3,
    "auditEnabled": true
  }'
```

### 4. Batch Duplicate Detection
```bash
curl -X POST http://localhost:8080/api/patients/duplicates/batch \
  -H "Content-Type: application/json" \
  -d '{
    "facilityId": "1",
    "dateFrom": "2024-01-01T00:00:00Z",
    "dateTo": "2024-12-31T23:59:59Z",
    "threshold": 70
  }'
```

## Response Examples

### Duplicate Detection Result
```json
{
  "isDuplicate": true,
  "confidenceLevel": "HIGH",
  "overallScore": 87,
  "potentialDuplicates": [
    {
      "patientId": "001-00-0000-0001",
      "fullName": "John Doe",
      "dateOfBirth": "1990-01-01",
      "phone": "+91-**********",
      "email": "<EMAIL>",
      "facilityId": "1",
      "matchScore": 87,
      "matchingCriteria": [
        {
          "field": "fullName",
          "matchType": "EXACT",
          "score": 40,
          "weight": 40,
          "originalValue": "John Doe",
          "matchedValue": "John Doe",
          "similarity": 1.0
        },
        {
          "field": "dateOfBirth",
          "matchType": "EXACT",
          "score": 30,
          "weight": 30,
          "originalValue": "1990-01-01",
          "matchedValue": "1990-01-01",
          "similarity": 1.0
        }
      ],
      "registrationDate": "2024-01-15T10:30:00Z"
    }
  ],
  "action": "BLOCK_REGISTRATION",
  "message": "High confidence duplicate detected - registration blocked",
  "detectionTime": "2024-01-20T14:25:30Z"
}
```

### Registration Blocked Response
```json
{
  "error": "DUPLICATE_DETECTED",
  "message": "Registration blocked due to high confidence duplicate detection. Potential duplicates found: 1. Please review existing patients or contact administrator.",
  "action": "BLOCKED"
}
```

## Performance Considerations

1. **Timeout Protection**: All duplicate checks have a 2-second timeout by default
2. **Indexed Queries**: Database queries are optimized with appropriate indexes
3. **Caching**: Configuration values are cached to reduce database load
4. **Batch Processing**: Large-scale duplicate detection uses pagination
5. **Async Processing**: Future enhancement for background duplicate detection

## Monitoring and Reporting

### Statistics Available
- Total detections per period
- High/medium/low confidence counts
- Blocked vs. flagged registrations
- Pending manual reviews
- False positive rates

### Audit Trail
- All duplicate detection events are logged
- Manual resolution actions are tracked
- Configuration changes are audited
- Performance metrics are recorded

## Security and Privacy

1. **Access Control**: Admin endpoints require appropriate permissions
2. **Data Protection**: Sensitive patient data is handled securely
3. **Audit Logging**: All actions are logged for compliance
4. **Configuration Validation**: Input validation prevents malicious configuration

## Troubleshooting

### Common Issues
1. **High False Positive Rate**: Adjust fuzzy matching thresholds
2. **Performance Issues**: Check database indexes and query performance
3. **Configuration Errors**: Validate weight values and thresholds
4. **Timeout Issues**: Increase timeout or optimize queries

### Debugging
- Enable detailed logging: `quarkus.log.category."sirobilt.meghasanjivini.patientregistration.service".level=DEBUG`
- Check duplicate detection logs table for detailed matching information
- Use manual duplicate check endpoint to test specific scenarios

## Future Enhancements

1. **Machine Learning**: AI-powered duplicate detection
2. **Real-time Notifications**: Alert administrators of high-confidence duplicates
3. **Advanced Phonetic Matching**: Support for multiple languages
4. **Integration APIs**: Connect with external patient registries
5. **Mobile App Support**: Duplicate detection for mobile registration
