package sirobilt.meghasanjivini.common.controller

import io.quarkus.test.junit.QuarkusTest
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.junit.jupiter.api.MethodOrderer

@QuarkusTest
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class UpidConfigurationControllerTest {

    @Test
    @org.junit.jupiter.api.Order(1)
    fun `should get current configuration`() {
        given()
            .`when`().get("/api/upid-config/current")
            .then()
            .statusCode(200)
            .body("formatType", notNullValue())
            .body("formatInfo", notNullValue())
            .body("sequenceConfig", notNullValue())
            .body("validationConfig", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(2)
    fun `should get available formats`() {
        given()
            .`when`().get("/api/upid-config/formats")
            .then()
            .statusCode(200)
            .body("size()", greaterThan(0))
            .body("[0].type", notNullValue())
            .body("[0].description", notNullValue())
            .body("[0].example", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(3)
    fun `should generate sample UPID`() {
        given()
            .`when`().post("/api/upid-config/generate-sample/1")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("upid", notNullValue())
            .body("facilityId", equalTo("1"))
            .body("formatType", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(4)
    fun `should validate UPID`() {
        val validationRequest = mapOf("upid" to "001-00-0000-0001")

        given()
            .contentType(ContentType.JSON)
            .body(validationRequest)
            .`when`().post("/api/upid-config/validate")
            .then()
            .statusCode(200)
            .body("upid", equalTo("001-00-0000-0001"))
            .body("formatType", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(5)
    fun `should perform health check`() {
        given()
            .`when`().get("/api/upid-config/health")
            .then()
            .statusCode(200)
            .body("status", equalTo("healthy"))
            .body("formatType", notNullValue())
            .body("testUpid", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(6)
    fun `should get effective configuration`() {
        given()
            .`when`().get("/api/upid-config/admin/effective")
            .then()
            .statusCode(200)
            .body("'upid.format.type'", notNullValue())
            .body("'upid.sequence.global'", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(7)
    fun `should validate configuration changes`() {
        val validationRequest = mapOf(
            "changes" to mapOf(
                "upid.format.standard.facility-digits" to "4"
            ),
            "validateOnly" to true
        )

        given()
            .contentType(ContentType.JSON)
            .body(validationRequest)
            .`when`().post("/api/upid-config/admin/validate")
            .then()
            .statusCode(200)
            .body("isValid", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(8)
    fun `should preview configuration changes`() {
        val previewRequest = mapOf(
            "upid.format.standard.facility-digits" to "4",
            "upid.format.standard.separator" to "_"
        )

        given()
            .contentType(ContentType.JSON)
            .body(previewRequest)
            .`when`().post("/api/upid-config/admin/preview")
            .then()
            .statusCode(200)
            .body("validation", notNullValue())
            .body("currentConfig", notNullValue())
            .body("newConfig", notNullValue())
    }

    @Test
    @org.junit.jupiter.api.Order(9)
    fun `should apply configuration changes`() {
        val changeRequest = mapOf(
            "changes" to mapOf(
                "upid.format.standard.facility-digits" to "4"
            ),
            "changeReason" to "Test configuration change",
            "changedBy" to "test-user"
        )

        given()
            .contentType(ContentType.JSON)
            .body(changeRequest)
            .`when`().put("/api/upid-config/admin/apply")
            .then()
            .statusCode(200)
            .body("isValid", equalTo(true))
    }

    @Test
    @org.junit.jupiter.api.Order(10)
    fun `should get configuration history`() {
        given()
            .`when`().get("/api/upid-config/admin/history")
            .then()
            .statusCode(200)
            .body("size()", greaterThanOrEqualTo(0))
    }

    @Test
    @org.junit.jupiter.api.Order(11)
    fun `should get configuration history for specific key`() {
        given()
            .queryParam("configKey", "upid.format.standard.facility-digits")
            .`when`().get("/api/upid-config/admin/history")
            .then()
            .statusCode(200)
            .body("size()", greaterThanOrEqualTo(0))
    }

    @Test
    @org.junit.jupiter.api.Order(12)
    fun `should generate test samples`() {
        val testConfig = mapOf(
            "upid.format.type" to "CUSTOM",
            "upid.format.custom.template" to "TEST{FACILITY}{SEQUENCE:4}"
        )

        given()
            .contentType(ContentType.JSON)
            .body(testConfig)
            .`when`().post("/api/upid-config/admin/test-samples/1")
            .then()
            .statusCode(200)
            .body("validation", notNullValue())
            .body("facilityId", equalTo("1"))
    }

    @Test
    @org.junit.jupiter.api.Order(13)
    fun `should handle invalid facility ID for sample generation`() {
        given()
            .`when`().post("/api/upid-config/generate-sample/invalid")
            .then()
            .statusCode(200)
            .body("success", equalTo(false))
            .body("message", containsString("Failed"))
    }

    @Test
    @org.junit.jupiter.api.Order(14)
    fun `should handle invalid UPID validation`() {
        val validationRequest = mapOf("upid" to "invalid-upid-format")

        given()
            .contentType(ContentType.JSON)
            .body(validationRequest)
            .`when`().post("/api/upid-config/validate")
            .then()
            .statusCode(200)
            .body("valid", equalTo(false))
            .body("upid", equalTo("invalid-upid-format"))
    }

    @Test
    @org.junit.jupiter.api.Order(15)
    fun `should handle invalid configuration changes`() {
        val invalidChangeRequest = mapOf(
            "changes" to mapOf(
                "upid.format.type" to "INVALID_TYPE"
            ),
            "changeReason" to "Test invalid change",
            "changedBy" to "test-user"
        )

        given()
            .contentType(ContentType.JSON)
            .body(invalidChangeRequest)
            .`when`().put("/api/upid-config/admin/apply")
            .then()
            .statusCode(400)
            .body("error", containsString("validation failed"))
    }
}
