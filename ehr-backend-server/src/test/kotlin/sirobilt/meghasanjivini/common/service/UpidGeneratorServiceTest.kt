package sirobilt.meghasanjivini.common.service

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import sirobilt.meghasanjivini.common.config.UpidFormatType

@QuarkusTest
class UpidGeneratorServiceTest {

    @Inject
    lateinit var upidGeneratorService: UpidGeneratorService

    @Test
    fun `should generate UPID with standard format`() {
        // Given
        val facilityId = "1"
        
        // When
        val upid = upidGeneratorService.generateNextUpid(facilityId)
        
        // Then
        assertNotNull(upid)
        assertTrue(upid.isNotBlank())
        
        // Standard format should be like: 001-00-0000-0001
        val pattern = "^\\d{3}-\\d{2}-\\d{4}-\\d{4}$"
        assertTrue(upid.matches(Regex(pattern)), "UPID '$upid' should match standard format pattern")
        
        // Should start with facility ID padded to 3 digits
        assertTrue(upid.startsWith("001-"), "UPID should start with padded facility ID")
    }

    @Test
    fun `should generate unique UPIDs for same facility`() {
        // Given
        val facilityId = "2"

        // When
        val upid1 = upidGeneratorService.generateNextUpid(facilityId)
        val upid2 = upidGeneratorService.generateNextUpid(facilityId)

        // Then
        assertNotEquals(upid1, upid2, "Generated UPIDs should be unique")

        // Additional verification - extract sequence numbers
        val seq1 = extractSequenceNumber(upid1)
        val seq2 = extractSequenceNumber(upid2)
        assertTrue(seq2 > seq1, "Second UPID should have higher sequence number. UPID1: $upid1, UPID2: $upid2")
    }

    private fun extractSequenceNumber(upid: String): Long {
        // Extract sequence from format like 001-00-0000-0001
        val parts = upid.split("-")
        if (parts.size >= 4) {
            val sequencePart = parts[2] + parts[3]
            return sequencePart.toLongOrNull() ?: 0L
        }
        return 0L
    }

    @Test
    fun `should generate UPIDs for different facilities`() {
        // Given
        val facilityId1 = "1"
        val facilityId2 = "3"
        
        // When
        val upid1 = upidGeneratorService.generateNextUpid(facilityId1)
        val upid2 = upidGeneratorService.generateNextUpid(facilityId2)
        
        // Then
        assertNotEquals(upid1, upid2, "UPIDs for different facilities should be different")
        assertTrue(upid1.startsWith("001-"), "First UPID should start with facility 1")
        assertTrue(upid2.startsWith("003-"), "Second UPID should start with facility 3")
    }

    @Test
    fun `should validate UPID format correctly`() {
        // Given
        val validStandardUpid = "001-00-0000-0001"
        val invalidUpid = "invalid-upid"
        
        // When & Then
        assertTrue(upidGeneratorService.validateUpidFormat(validStandardUpid), 
                  "Valid standard UPID should pass validation")
        assertFalse(upidGeneratorService.validateUpidFormat(invalidUpid), 
                   "Invalid UPID should fail validation")
    }

    @Test
    fun `should handle facility ID validation`() {
        // Given
        val invalidFacilityId = "abc"
        
        // When & Then
        assertThrows(IllegalArgumentException::class.java) {
            upidGeneratorService.generateNextUpid(invalidFacilityId)
        }
    }
}
