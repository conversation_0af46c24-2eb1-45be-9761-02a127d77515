package sirobilt.meghasanjivini.patientregistration.service

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import sirobilt.meghasanjivini.patientregistration.dto.PatientRegistrationDto
import sirobilt.meghasanjivini.patientregistration.model.Gender
import sirobilt.meghasanjivini.patientregistration.model.IdentifierType
import java.time.LocalDate

@QuarkusTest
class PatientServiceUpidTest {

    @Inject
    lateinit var patientService: PatientService

    @Test
    @Transactional
    fun `should register patient with configurable UPID`() {
        // Given
        val patientDto = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1990, 1, 1),
            age = 30,
            gender = Gender.Male
        )

        // When
        val result = patientService.register(patientDto)

        // Then
        assertNotNull(result)
        assertNotNull(result.patientId)
        assertTrue(result.patientId.isNotBlank())
        
        // Should follow standard format: 002-00-XXXX-XXXX
        val pattern = "^\\d{3}-\\d{2}-\\d{4}-\\d{4}$"
        assertTrue(result.patientId.matches(Regex(pattern)), 
                  "Patient ID '${result.patientId}' should match standard UPID format")
        
        // Should start with facility ID 2 padded to 3 digits
        assertTrue(result.patientId.startsWith("002-"), 
                  "Patient ID should start with padded facility ID")
        
        assertEquals("John", result.firstName)
        assertEquals("Doe", result.lastName)
        assertEquals("2", result.facilityId)
    }

    @Test
    @Transactional
    fun `should generate sequential UPIDs for multiple patients`() {
        // Given
        val patient1Dto = PatientRegistrationDto(
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "11-1111-1111-1111",
            firstName = "Alice",
            lastName = "Smith",
            age = 25,
            gender = Gender.FEMALE
        )
        
        val patient2Dto = PatientRegistrationDto(
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "22-2222-2222-2222",
            firstName = "Bob",
            lastName = "Johnson",
            age = 35,
            gender = Gender.MALE
        )

        // When
        val result1 = patientService.register(patient1Dto)
        val result2 = patientService.register(patient2Dto)

        // Then
        assertNotNull(result1.patientId)
        assertNotNull(result2.patientId)
        assertNotEquals(result1.patientId, result2.patientId)
        
        // Both should start with facility ID 1
        assertTrue(result1.patientId.startsWith("001-"))
        assertTrue(result2.patientId.startsWith("001-"))
        
        // Extract sequence numbers and verify they are sequential
        val seq1 = extractSequenceNumber(result1.patientId)
        val seq2 = extractSequenceNumber(result2.patientId)
        
        assertTrue(seq2 > seq1, "Second patient should have higher sequence number")
    }

    @Test
    @Transactional
    fun `should handle cross-facility sequential numbering`() {
        // Given
        val facilityAPatient = PatientRegistrationDto(
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "33-3333-3333-3333",
            firstName = "Charlie",
            lastName = "Brown",
            age = 40,
            gender = Gender.MALE
        )
        
        val facilityBPatient = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "44-4444-4444-4444",
            firstName = "Diana",
            lastName = "Prince",
            age = 30,
            gender = Gender.FEMALE
        )

        // When
        val resultA = patientService.register(facilityAPatient)
        val resultB = patientService.register(facilityBPatient)

        // Then
        assertTrue(resultA.patientId.startsWith("001-"))
        assertTrue(resultB.patientId.startsWith("002-"))
        
        // Sequence numbers should be continuous across facilities
        val seqA = extractSequenceNumber(resultA.patientId)
        val seqB = extractSequenceNumber(resultB.patientId)
        
        assertTrue(seqB > seqA, "Global sequence should be continuous across facilities")
    }

    @Test
    fun `should validate facility ID format`() {
        // Given
        val invalidPatientDto = PatientRegistrationDto(
            facilityId = "invalid",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "55-5555-5555-5555",
            firstName = "Invalid",
            lastName = "Patient",
            age = 25,
            gender = Gender.MALE
        )

        // When & Then
        assertThrows(IllegalArgumentException::class.java) {
            patientService.register(invalidPatientDto)
        }
    }

    @Test
    fun `should generate UPID using new method`() {
        // Given
        val facilityId = "3"

        // When
        val upid = patientService.generateNextUpid(facilityId)

        // Then
        assertNotNull(upid)
        assertTrue(upid.startsWith("003-"))
        assertTrue(upid.matches(Regex("^\\d{3}-\\d{2}-\\d{4}-\\d{4}$")))
    }

    @Test
    fun `should maintain backward compatibility with deprecated method`() {
        // Given
        val facilityId = "4"

        // When
        @Suppress("DEPRECATION")
        val mrn = patientService.generateNextMrn(facilityId, null)

        // Then
        assertNotNull(mrn)
        assertTrue(mrn.startsWith("004-"))
        assertTrue(mrn.matches(Regex("^\\d{3}-\\d{2}-\\d{4}-\\d{4}$")))
    }

    private fun extractSequenceNumber(upid: String): Long {
        // Extract sequence from format like 001-00-0000-0001
        val parts = upid.split("-")
        if (parts.size >= 4) {
            val sequencePart = parts[2] + parts[3]
            return sequencePart.toLongOrNull() ?: 0L
        }
        return 0L
    }
}
