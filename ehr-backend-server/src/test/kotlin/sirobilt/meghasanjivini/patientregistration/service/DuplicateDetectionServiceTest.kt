package sirobilt.meghasanjivini.patientregistration.service

import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.*
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.LocalDate

@QuarkusTest
class DuplicateDetectionServiceTest {

    @Inject
    lateinit var duplicateDetectionService: DuplicateDetectionService

    @Inject
    lateinit var patientRepository: PatientRepository

    @Inject
    lateinit var configService: DuplicateDetectionConfigService

    @BeforeEach
    @Transactional
    fun setup() {
        // Clean up any existing test data
        patientRepository.deleteAll()
    }

    @Test
    @Transactional
    fun `should detect exact name and DOB duplicate with high confidence`() {
        // Given - Create an existing patient
        val existingPatient = Patient(
            upId = "001-00-0000-0001",
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1990, 1, 1),
            gender = Gender.Male
        )
        patientRepository.persist(existingPatient)

        // When - Try to register a patient with same name and DOB
        val candidateData = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "98-7654-3210-9876",
            firstName = "John",
            lastName = "Doe",
            dateOfBirth = LocalDate.of(1990, 1, 1),
            gender = Gender.Male,
            age = 34
        )

        val result = duplicateDetectionService.detectDuplicates(candidateData)

        // Then - Should detect high confidence duplicate
        assertTrue(result.isDuplicate)
        assertEquals(ConfidenceLevel.HIGH, result.confidenceLevel)
        assertEquals(DuplicateAction.BLOCK_REGISTRATION, result.action)
        assertTrue(result.overallScore >= configService.getHighThreshold())
        assertEquals(1, result.potentialDuplicates.size)
        assertEquals("001-00-0000-0001", result.potentialDuplicates.first().patientId)
    }

    @Test
    @Transactional
    fun `should detect fuzzy name match with medium confidence`() {
        // Given - Create an existing patient
        val existingPatient = Patient(
            upId = "001-00-0000-0002",
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "John",
            lastName = "Smith",
            dateOfBirth = LocalDate.of(1985, 5, 15),
            gender = Gender.Male
        )
        patientRepository.persist(existingPatient)

        // When - Try to register a patient with similar name (typo)
        val candidateData = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "98-7654-3210-9876",
            firstName = "Jon", // Typo in first name
            lastName = "Smyth", // Typo in last name
            dateOfBirth = LocalDate.of(1985, 5, 15),
            gender = Gender.Male,
            age = 39
        )

        val result = duplicateDetectionService.detectDuplicates(candidateData)

        // Then - Should detect medium confidence duplicate
        assertTrue(result.isDuplicate)
        assertTrue(result.confidenceLevel == ConfidenceLevel.MEDIUM || result.confidenceLevel == ConfidenceLevel.HIGH)
        assertTrue(result.action == DuplicateAction.FLAG_FOR_REVIEW || result.action == DuplicateAction.BLOCK_REGISTRATION)
        assertTrue(result.overallScore >= configService.getMediumThreshold())
        assertEquals(1, result.potentialDuplicates.size)
    }

    @Test
    @Transactional
    fun `should detect phone number match`() {
        // Given - Create an existing patient with contact
        val existingPatient = Patient(
            upId = "001-00-0000-0003",
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "Alice",
            lastName = "Johnson",
            dateOfBirth = LocalDate.of(1992, 3, 10),
            gender = Gender.Female
        )
        val contact = PatientContact(
            patient = existingPatient,
            phoneNumber = "+91-**********",
            email = "<EMAIL>"
        )
        existingPatient.contacts.add(contact)
        patientRepository.persist(existingPatient)

        // When - Try to register a patient with same phone number but different name
        val candidateData = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "98-7654-3210-9876",
            firstName = "Alice",
            lastName = "Williams", // Different last name
            dateOfBirth = LocalDate.of(1993, 8, 20), // Different DOB
            gender = Gender.Female,
            age = 31,
            contacts = listOf(
                ContactDto(
                    mobileNumber = "+91-**********",
                    phoneNumber = "+91-**********", // Same phone number
                    email = "<EMAIL>"
                )
            )
        )

        val result = duplicateDetectionService.detectDuplicates(candidateData)

        // Then - Should detect potential duplicate based on phone match
        assertTrue(result.isDuplicate)
        assertTrue(result.overallScore > 0)
        assertEquals(1, result.potentialDuplicates.size)
        
        // Check that phone number matching criterion is present
        val phoneMatch = result.potentialDuplicates.first().matchingCriteria
            .find { it.field == "phoneNumber" }
        assertNotNull(phoneMatch)
        assertEquals(MatchType.EXACT, phoneMatch?.matchType)
    }

    @Test
    @Transactional
    fun `should not detect duplicate for completely different patients`() {
        // Given - Create an existing patient
        val existingPatient = Patient(
            upId = "001-00-0000-0004",
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "Robert",
            lastName = "Brown",
            dateOfBirth = LocalDate.of(1980, 12, 25),
            gender = Gender.Male
        )
        patientRepository.persist(existingPatient)

        // When - Try to register a completely different patient
        val candidateData = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "98-7654-3210-9876",
            firstName = "Sarah",
            lastName = "Wilson",
            dateOfBirth = LocalDate.of(1995, 6, 30),
            gender = Gender.Female,
            age = 29
        )

        val result = duplicateDetectionService.detectDuplicates(candidateData)

        // Then - Should not detect any duplicates
        assertFalse(result.isDuplicate)
        assertEquals(ConfidenceLevel.LOW, result.confidenceLevel)
        assertEquals(DuplicateAction.ALLOW_REGISTRATION, result.action)
        assertEquals(0, result.overallScore)
        assertTrue(result.potentialDuplicates.isEmpty())
    }

    @Test
    @Transactional
    fun `should detect ABHA number duplicate with high confidence`() {
        // Given - Create an existing patient with ABHA
        val existingPatient = Patient(
            upId = "001-00-0000-0005",
            facilityId = "1",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "12-3456-7890-1234",
            firstName = "Michael",
            lastName = "Davis",
            dateOfBirth = LocalDate.of(1988, 9, 5),
            gender = Gender.Male
        )
        val abha = PatientAbha(
            patient = existingPatient,
            abhaNumber = "12-3456-7890-1234",
            abhaAddress = "michael.davis@abha"
        )
        existingPatient.abha = abha
        patientRepository.persist(existingPatient)

        // When - Try to register a patient with same ABHA number
        val candidateData = PatientRegistrationDto(
            facilityId = "2",
            identifierType = IdentifierType.ABHA,
            identifierNumber = "98-7654-3210-9876",
            firstName = "Mike", // Different name
            lastName = "Davidson", // Different name
            dateOfBirth = LocalDate.of(1989, 10, 15), // Different DOB
            gender = Gender.Male,
            age = 35,
            abha = AbhaDto(
                abhaNumber = "12-3456-7890-1234", // Same ABHA number
                abhaAddress = "mike.davidson@abha"
            )
        )

        val result = duplicateDetectionService.detectDuplicates(candidateData)

        // Then - Should detect high confidence duplicate based on ABHA match
        assertTrue(result.isDuplicate)
        assertEquals(ConfidenceLevel.HIGH, result.confidenceLevel)
        assertEquals(DuplicateAction.BLOCK_REGISTRATION, result.action)
        assertTrue(result.overallScore >= configService.getHighThreshold())
        assertEquals(1, result.potentialDuplicates.size)
        
        // Check that ABHA matching criterion is present
        val abhaMatch = result.potentialDuplicates.first().matchingCriteria
            .find { it.field == "abhaNumber" }
        assertNotNull(abhaMatch)
        assertEquals(MatchType.EXACT, abhaMatch?.matchType)
    }

    @Test
    fun `should respect configuration settings`() {
        // Given - Get current configuration
        val config = configService.getCurrentConfig()
        
        // Then - Configuration should be loaded correctly
        assertTrue(config.enabled)
        assertTrue(config.highThreshold > config.mediumThreshold)
        assertTrue(config.mediumThreshold > 0)
        assertTrue(config.timeoutSeconds > 0)
        assertTrue(config.weights.nameExact > 0)
        assertTrue(config.weights.dateOfBirth > 0)
    }

    @Test
    fun `should return statistics`() {
        // When - Get detection statistics (this will fail gracefully due to missing tables in test)
        try {
            val statistics = duplicateDetectionService.getDetectionStatistics(7)

            // Then - Should return valid statistics
            assertNotNull(statistics)
            assertTrue(statistics.containsKey("period_days"))
            assertEquals(7, statistics["period_days"])
        } catch (e: Exception) {
            // Expected in test environment without audit tables
            assertTrue(e.message?.contains("DUPLICATE_DETECTION_LOGS") == true)
        }
    }
}
