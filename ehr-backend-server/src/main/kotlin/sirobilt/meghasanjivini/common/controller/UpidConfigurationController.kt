package sirobilt.meghasanjivini.common.controller

import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType
import org.eclipse.microprofile.openapi.annotations.media.Content
import org.eclipse.microprofile.openapi.annotations.media.ExampleObject
import org.eclipse.microprofile.openapi.annotations.media.Schema
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.common.config.UpidConfiguration
import sirobilt.meghasanjivini.common.config.UpidFormatMetadata
import sirobilt.meghasanjivini.common.config.UpidFormatType
import sirobilt.meghasanjivini.common.model.*
import sirobilt.meghasanjivini.common.service.UpidGeneratorService
import sirobilt.meghasanjivini.common.service.UpidConfigurationAdminService

/**
 * REST Controller for UPID (Unique Patient ID) configuration management
 * Provides both read-only endpoints for viewing configuration and admin endpoints for managing it
 */
@Tag(name = "UPID Configuration", description = "UPID (Unique Patient ID) configuration management APIs")
@Path("/api/upid-config")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class UpidConfigurationController @Inject constructor(
    private val upidConfig: UpidConfiguration,
    private val upidGenerator: UpidGeneratorService,
    private val adminService: UpidConfigurationAdminService
) {

    // ========== READ-ONLY ENDPOINTS ==========

    @GET
    @Path("/current")
    @Operation(
        summary = "Get current UPID configuration",
        description = "Retrieves the current effective UPID configuration including format, sequence, and validation settings"
    )
    @APIResponses(
        APIResponse(
            responseCode = "200",
            description = "Current configuration retrieved successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = UpidConfigurationResponse::class),
                examples = [ExampleObject(
                    name = "Standard Configuration",
                    value = """{
                        "formatType": "STANDARD",
                        "formatInfo": {
                            "type": "STANDARD",
                            "description": "Standard format with facility, network, and sequence",
                            "example": "001-00-0000-0001"
                        },
                        "sequenceConfig": {
                            "global": true,
                            "startFrom": 1
                        },
                        "validationConfig": {
                            "checkDuplicates": true,
                            "allowManualOverride": false
                        }
                    }"""
                )]
            )]
        )
    )
    fun getCurrentConfiguration(): UpidConfigurationResponse {
        return UpidConfigurationResponse(
            formatType = upidConfig.format().type(),
            formatInfo = upidGenerator.getFormatInfo(),
            sequenceConfig = SequenceConfigResponse(
                global = upidConfig.sequence().global(),
                startFrom = upidConfig.sequence().startFrom(),
                resetAnnually = upidConfig.sequence().resetAnnually(),
                resetMonthly = upidConfig.sequence().resetMonthly()
            ),
            validationConfig = ValidationConfigResponse(
                checkDuplicates = upidConfig.validation().checkDuplicates(),
                allowManualOverride = upidConfig.validation().allowManualOverride(),
                reservedPatterns = upidConfig.validation().reservedPatterns().split(",")
            )
        )
    }

    @GET
    @Path("/formats")
    @Operation(
        summary = "Get available UPID formats",
        description = "Lists all available UPID format types with their descriptions, examples, and validation patterns"
    )
    @APIResponse(
        responseCode = "200",
        description = "Available formats retrieved successfully",
        content = [Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = Schema(implementation = UpidFormatInfo::class, type = SchemaType.ARRAY)
        )]
    )
    fun getAvailableFormats(): List<UpidFormatInfo> {
        return UpidFormatType.values().map { formatType ->
            val metadata = UpidFormatMetadata.getMetadata(formatType)
            UpidFormatInfo(
                type = formatType,
                description = metadata.description,
                example = metadata.example,
                pattern = metadata.pattern,
                maxLength = metadata.maxLength,
                minLength = metadata.minLength
            )
        }
    }

    @POST
    @Path("/generate-sample/{facilityId}")
    @Operation(
        summary = "Generate sample UPID",
        description = "Generates a sample UPID for the specified facility using current configuration"
    )
    @APIResponses(
        APIResponse(responseCode = "200", description = "Sample UPID generated successfully"),
        APIResponse(responseCode = "400", description = "Invalid facility ID"),
        APIResponse(responseCode = "500", description = "Failed to generate sample UPID")
    )
    fun generateSampleUpid(
        @Parameter(description = "Facility ID for sample generation", example = "1")
        @PathParam("facilityId") facilityId: String
    ): SampleUpidResponse {
        return try {
            val sampleUpid = upidGenerator.generateNextUpid(facilityId)
            SampleUpidResponse(
                success = true,
                upid = sampleUpid,
                facilityId = facilityId,
                formatType = upidConfig.format().type(),
                message = "Sample UPID generated successfully"
            )
        } catch (e: Exception) {
            SampleUpidResponse(
                success = false,
                upid = null,
                facilityId = facilityId,
                formatType = upidConfig.format().type(),
                message = "Failed to generate sample UPID: ${e.message}"
            )
        }
    }

    @POST
    @Path("/validate")
    @Operation(
        summary = "Validate UPID format",
        description = "Validates whether a UPID conforms to the current format configuration"
    )
    @APIResponses(
        APIResponse(responseCode = "200", description = "Validation completed"),
        APIResponse(responseCode = "400", description = "Invalid request")
    )
    fun validateUpid(
        @RequestBody(
            description = "UPID validation request",
            content = [Content(
                examples = [ExampleObject(
                    name = "Standard UPID",
                    value = """{"upid": "001-00-0000-0001"}"""
                )]
            )]
        )
        request: UpidValidationRequest
    ): UpidValidationResponse {
        return try {
            val isValid = upidGenerator.validateUpid(request.upid)
            UpidValidationResponse(
                valid = isValid,
                upid = request.upid,
                formatType = upidConfig.format().type(),
                message = if (isValid) "UPID is valid" else "UPID does not conform to current format"
            )
        } catch (e: Exception) {
            UpidValidationResponse(
                valid = false,
                upid = request.upid,
                formatType = upidConfig.format().type(),
                message = "Validation failed: ${e.message}"
            )
        }
    }

    @GET
    @Path("/health")
    @Operation(
        summary = "UPID configuration health check",
        description = "Performs a health check on the UPID configuration system"
    )
    @APIResponses(
        APIResponse(responseCode = "200", description = "System is healthy"),
        APIResponse(responseCode = "503", description = "System is unhealthy")
    )
    fun healthCheck(): Response {
        return try {
            val formatType = upidConfig.format().type()
            val testUpid = upidGenerator.generateNextUpid("1")

            Response.ok(mapOf(
                "status" to "healthy",
                "formatType" to formatType.name,
                "testUpid" to testUpid,
                "timestamp" to System.currentTimeMillis()
            )).build()
        } catch (e: Exception) {
            Response.status(Response.Status.SERVICE_UNAVAILABLE)
                .entity(mapOf(
                    "status" to "unhealthy",
                    "error" to e.message,
                    "timestamp" to System.currentTimeMillis()
                )).build()
        }
    }

    // ========== ADMIN ENDPOINTS ==========

    @GET
    @Path("/admin/effective")
    @Operation(
        summary = "[ADMIN] Get effective configuration",
        description = "Retrieves the complete effective configuration including both static and dynamic settings"
    )
    @APIResponse(
        responseCode = "200",
        description = "Effective configuration retrieved successfully"
    )
    fun getEffectiveConfiguration(): Map<String, String> {
        return adminService.getCurrentConfiguration()
    }

    @POST
    @Path("/admin/validate")
    @Operation(
        summary = "[ADMIN] Validate configuration changes",
        description = "Validates proposed configuration changes without applying them"
    )
    @APIResponses(
        APIResponse(responseCode = "200", description = "Validation completed"),
        APIResponse(responseCode = "400", description = "Invalid configuration")
    )
    fun validateConfigurationChanges(
        @RequestBody(
            description = "Configuration changes to validate",
            content = [Content(
                examples = [ExampleObject(
                    name = "Change Format Type",
                    value = """{
                        "changes": {
                            "upid.format.type": "CUSTOM",
                            "upid.format.custom.template": "HSP{FACILITY}{YEAR}{SEQUENCE:6}"
                        },
                        "validateOnly": true
                    }"""
                )]
            )]
        )
        request: ConfigurationChangeRequest
    ): ConfigurationValidationResult {
        return adminService.validateConfiguration(request.changes)
    }

    @POST
    @Path("/admin/preview")
    @Operation(
        summary = "[ADMIN] Preview configuration changes",
        description = "Previews the impact of configuration changes with sample UPIDs"
    )
    @APIResponse(
        responseCode = "200",
        description = "Configuration preview generated successfully"
    )
    fun previewConfigurationChanges(
        @RequestBody(
            description = "Configuration changes to preview",
            content = [Content(
                examples = [ExampleObject(
                    name = "Preview Numeric Format",
                    value = """{
                        "upid.format.type": "NUMERIC",
                        "upid.format.numeric.prefix": "2",
                        "upid.format.numeric.total-digits": "12"
                    }"""
                )]
            )]
        )
        changes: Map<String, String>
    ): Map<String, Any> {
        return adminService.previewConfiguration(changes)
    }

    @PUT
    @Path("/admin/apply")
    @Operation(
        summary = "[ADMIN] Apply configuration changes",
        description = "Applies validated configuration changes to the system"
    )
    @APIResponses(
        APIResponse(responseCode = "200", description = "Configuration applied successfully"),
        APIResponse(responseCode = "400", description = "Invalid configuration"),
        APIResponse(responseCode = "500", description = "Failed to apply configuration")
    )
    fun applyConfigurationChanges(
        @RequestBody(
            description = "Configuration changes to apply",
            content = [Content(
                examples = [ExampleObject(
                    name = "Apply Standard Format Changes",
                    value = """{
                        "changes": {
                            "upid.format.standard.facility-digits": "4",
                            "upid.format.standard.separator": "_"
                        },
                        "changeReason": "Updated for new facility numbering system",
                        "changedBy": "<EMAIL>"
                    }"""
                )]
            )]
        )
        @Valid request: ConfigurationChangeRequest
    ): Response {
        return try {
            val result = adminService.applyConfiguration(request)
            if (result.isValid) {
                Response.ok(result).build()
            } else {
                Response.status(Response.Status.BAD_REQUEST)
                    .entity(mapOf(
                        "error" to "Configuration validation failed",
                        "details" to result.errors
                    )).build()
            }
        } catch (e: Exception) {
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf(
                    "error" to "Failed to apply configuration",
                    "message" to e.message
                )).build()
        }
    }

    @POST
    @Path("/admin/rollback")
    @Operation(
        summary = "[ADMIN] Rollback configuration",
        description = "Rolls back a configuration setting to a previous value"
    )
    @APIResponses(
        APIResponse(responseCode = "200", description = "Configuration rolled back successfully"),
        APIResponse(responseCode = "400", description = "Invalid rollback request"),
        APIResponse(responseCode = "404", description = "No rollback target found"),
        APIResponse(responseCode = "500", description = "Failed to rollback configuration")
    )
    fun rollbackConfiguration(
        @RequestBody(
            description = "Rollback request",
            content = [Content(
                examples = [ExampleObject(
                    name = "Rollback Format Type",
                    value = """{
                        "configKey": "upid.format.type",
                        "rollbackReason": "Reverting due to compatibility issues",
                        "rolledBackBy": "<EMAIL>"
                    }"""
                )]
            )]
        )
        @Valid request: ConfigurationRollbackRequest
    ): Response {
        return try {
            val success = adminService.rollbackConfiguration(request)
            if (success) {
                Response.ok(mapOf(
                    "success" to true,
                    "message" to "Configuration rolled back successfully"
                )).build()
            } else {
                Response.status(Response.Status.NOT_FOUND)
                    .entity(mapOf(
                        "error" to "No rollback target found",
                        "configKey" to request.configKey
                    )).build()
            }
        } catch (e: Exception) {
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf(
                    "error" to "Failed to rollback configuration",
                    "message" to e.message
                )).build()
        }
    }

    @GET
    @Path("/admin/history")
    @Operation(
        summary = "[ADMIN] Get configuration history",
        description = "Retrieves the history of configuration changes"
    )
    @APIResponse(
        responseCode = "200",
        description = "Configuration history retrieved successfully"
    )
    fun getConfigurationHistory(
        @Parameter(description = "Specific configuration key to get history for")
        @QueryParam("configKey") configKey: String?
    ): List<UpidConfigurationHistoryEntity> {
        return adminService.getConfigurationHistory(configKey)
    }

    @POST
    @Path("/admin/test-samples/{facilityId}")
    @Operation(
        summary = "[ADMIN] Generate test samples with custom config",
        description = "Generates sample UPIDs using a temporary configuration for testing"
    )
    @APIResponse(
        responseCode = "200",
        description = "Test samples generated successfully"
    )
    fun generateTestSamples(
        @Parameter(description = "Facility ID for sample generation", example = "1")
        @PathParam("facilityId") facilityId: String,
        @RequestBody(
            description = "Temporary configuration for testing",
            content = [Content(
                examples = [ExampleObject(
                    name = "Test Custom Format",
                    value = """{
                        "upid.format.type": "CUSTOM",
                        "upid.format.custom.template": "TEST{FACILITY}{SEQUENCE:4}"
                    }"""
                )]
            )]
        )
        testConfig: Map<String, String>
    ): Response {
        return try {
            val validation = adminService.validateConfiguration(testConfig)
            Response.ok(mapOf(
                "validation" to validation,
                "facilityId" to facilityId,
                "testConfig" to testConfig
            )).build()
        } catch (e: Exception) {
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf(
                    "error" to "Failed to generate test samples",
                    "message" to e.message
                )).build()
        }
    }
}

// ========== DTOs for API Requests and Responses ==========

@Schema(description = "UPID configuration response")
data class UpidConfigurationResponse(
    @Schema(description = "Current format type", example = "STANDARD")
    val formatType: UpidFormatType,

    @Schema(description = "Format-specific information")
    val formatInfo: Map<String, Any>,

    @Schema(description = "Sequence configuration")
    val sequenceConfig: SequenceConfigResponse,

    @Schema(description = "Validation configuration")
    val validationConfig: ValidationConfigResponse
)

@Schema(description = "Sequence configuration response")
data class SequenceConfigResponse(
    @Schema(description = "Whether to use global sequence", example = "true")
    val global: Boolean,

    @Schema(description = "Starting sequence number", example = "1")
    val startFrom: Long,

    @Schema(description = "Whether to reset sequence annually", example = "false")
    val resetAnnually: Boolean,

    @Schema(description = "Whether to reset sequence monthly", example = "false")
    val resetMonthly: Boolean
)

@Schema(description = "Validation configuration response")
data class ValidationConfigResponse(
    @Schema(description = "Whether to check for duplicates", example = "true")
    val checkDuplicates: Boolean,

    @Schema(description = "Whether to allow manual override", example = "false")
    val allowManualOverride: Boolean,

    @Schema(description = "List of reserved patterns")
    val reservedPatterns: List<String>
)

@Schema(description = "UPID format information")
data class UpidFormatInfo(
    @Schema(description = "Format type", example = "STANDARD")
    val type: UpidFormatType,

    @Schema(description = "Format description", example = "Standard format with facility, network, and sequence")
    val description: String,

    @Schema(description = "Example UPID", example = "001-00-0000-0001")
    val example: String,

    @Schema(description = "Validation pattern (regex)")
    val pattern: String?,

    @Schema(description = "Maximum length")
    val maxLength: Int?,

    @Schema(description = "Minimum length")
    val minLength: Int?
)

@Schema(description = "Sample UPID generation response")
data class SampleUpidResponse(
    @Schema(description = "Whether generation was successful", example = "true")
    val success: Boolean,

    @Schema(description = "Generated UPID", example = "001-00-0000-0001")
    val upid: String?,

    @Schema(description = "Facility ID used", example = "1")
    val facilityId: String,

    @Schema(description = "Format type used", example = "STANDARD")
    val formatType: UpidFormatType,

    @Schema(description = "Result message")
    val message: String
)

@Schema(description = "UPID validation request")
data class UpidValidationRequest(
    @Schema(description = "UPID to validate", example = "001-00-0000-0001", required = true)
    val upid: String
)

@Schema(description = "UPID validation response")
data class UpidValidationResponse(
    @Schema(description = "Whether UPID is valid", example = "true")
    val valid: Boolean,

    @Schema(description = "UPID that was validated", example = "001-00-0000-0001")
    val upid: String,

    @Schema(description = "Format type used for validation", example = "STANDARD")
    val formatType: UpidFormatType,

    @Schema(description = "Validation result message")
    val message: String
)
