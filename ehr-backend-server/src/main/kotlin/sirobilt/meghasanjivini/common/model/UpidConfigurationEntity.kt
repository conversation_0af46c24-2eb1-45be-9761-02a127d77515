package sirobilt.meghasanjivini.common.model

import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.*
import sirobilt.meghasanjivini.common.config.UpidFormatType
import java.time.OffsetDateTime

/**
 * Entity for persisting UPID configuration changes
 * Allows dynamic configuration updates that survive application restarts
 */
@Entity
@Table(name = "upid_configuration")
data class UpidConfigurationEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "config_key", nullable = false, unique = true, length = 100)
    val configKey: String = "",

    @Column(name = "config_value", nullable = false, length = 500)
    val configValue: String = "",

    @Column(name = "config_type", nullable = false, length = 50)
    val configType: String = "STRING",

    @Column(name = "description", length = 1000)
    val description: String? = null,

    @Column(name = "is_active", nullable = false)
    val isActive: Boolean = true,

    @Column(name = "created_at", nullable = false)
    val createdAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    val updatedAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "created_by", length = 100)
    val createdBy: String? = null,

    @Column(name = "updated_by", length = 100)
    val updatedBy: String? = null,

    @Version
    @JsonIgnore
    val version: Long? = null
)

/**
 * Entity for storing UPID configuration history and rollback capability
 */
@Entity
@Table(name = "upid_configuration_history")
data class UpidConfigurationHistoryEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    @Column(name = "config_key", nullable = false, length = 100)
    val configKey: String = "",

    @Column(name = "old_value", length = 500)
    val oldValue: String? = null,

    @Column(name = "new_value", nullable = false, length = 500)
    val newValue: String = "",

    @Column(name = "change_reason", length = 1000)
    val changeReason: String? = null,

    @Column(name = "changed_at", nullable = false)
    val changedAt: OffsetDateTime = OffsetDateTime.now(),

    @Column(name = "changed_by", length = 100)
    val changedBy: String? = null,

    @Column(name = "rollback_available", nullable = false)
    val rollbackAvailable: Boolean = true
)

/**
 * Predefined configuration keys for UPID settings
 */
object UpidConfigKeys {
    // Format configuration
    const val FORMAT_TYPE = "upid.format.type"
    
    // Standard format
    const val STANDARD_FACILITY_DIGITS = "upid.format.standard.facility-digits"
    const val STANDARD_NETWORK_DIGITS = "upid.format.standard.network-digits"
    const val STANDARD_SEQUENCE_DIGITS = "upid.format.standard.sequence-digits"
    const val STANDARD_SEPARATOR = "upid.format.standard.separator"
    const val STANDARD_NETWORK_ID = "upid.format.standard.network-id"
    
    // Custom format
    const val CUSTOM_TEMPLATE = "upid.format.custom.template"
    
    // Numeric format
    const val NUMERIC_PREFIX = "upid.format.numeric.prefix"
    const val NUMERIC_TOTAL_DIGITS = "upid.format.numeric.total-digits"
    
    // Alphanumeric format
    const val ALPHANUMERIC_PREFIX = "upid.format.alphanumeric.prefix"
    const val ALPHANUMERIC_FACILITY_DIGITS = "upid.format.alphanumeric.facility-digits"
    const val ALPHANUMERIC_SUFFIX_PATTERN = "upid.format.alphanumeric.suffix-pattern"
    
    // UUID format
    const val UUID_PREFIX = "upid.format.uuid.prefix"
    const val UUID_INCLUDE_FACILITY = "upid.format.uuid.include-facility"
    
    // Sequence configuration
    const val SEQUENCE_GLOBAL = "upid.sequence.global"
    const val SEQUENCE_START_FROM = "upid.sequence.start-from"
    const val SEQUENCE_RESET_ANNUALLY = "upid.sequence.reset-annually"
    const val SEQUENCE_RESET_MONTHLY = "upid.sequence.reset-monthly"
    
    // Validation configuration
    const val VALIDATION_CHECK_DUPLICATES = "upid.validation.check-duplicates"
    const val VALIDATION_ALLOW_MANUAL_OVERRIDE = "upid.validation.allow-manual-override"
    const val VALIDATION_RESERVED_PATTERNS = "upid.validation.reserved-patterns"
}

/**
 * Configuration validation result
 */
data class ConfigurationValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList(),
    val sampleUpids: Map<String, String> = emptyMap() // facilityId -> sample UPID
)

/**
 * Configuration change request
 */
data class ConfigurationChangeRequest(
    val changes: Map<String, String>,
    val changeReason: String? = null,
    val changedBy: String? = null,
    val validateOnly: Boolean = false
)

/**
 * Configuration rollback request
 */
data class ConfigurationRollbackRequest(
    val configKey: String,
    val targetHistoryId: Long? = null, // If null, rollback to previous version
    val rollbackReason: String? = null,
    val rolledBackBy: String? = null
)
