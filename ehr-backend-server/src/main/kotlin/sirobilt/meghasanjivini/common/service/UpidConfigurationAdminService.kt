package sirobilt.meghasanjivini.common.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.common.config.UpidConfiguration
import sirobilt.meghasanjivini.common.config.UpidFormatType
import sirobilt.meghasanjivini.common.model.*
import sirobilt.meghasanjivini.common.repository.UpidConfigurationRepository
import sirobilt.meghasanjivini.common.repository.UpidConfigurationHistoryRepository
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository

/**
 * Administrative service for managing UPID configuration
 * Provides capabilities for dynamic configuration updates with validation and rollback
 */
@ApplicationScoped
class UpidConfigurationAdminService @Inject constructor(
    private val configRepo: UpidConfigurationRepository,
    private val historyRepo: UpidConfigurationHistoryRepository,
    private val upidConfig: UpidConfiguration,
    private val upidGenerator: UpidGeneratorService,
    private val patientRepo: PatientRepository
) {

    companion object {
        private val logger: Logger = Logger.getLogger(UpidConfigurationAdminService::class.java)
    }

    /**
     * Get current effective configuration (combines static and dynamic config)
     */
    fun getCurrentConfiguration(): Map<String, String> {
        val staticConfig = getStaticConfiguration()
        val dynamicConfig = getDynamicConfiguration()
        
        // Dynamic config overrides static config
        return staticConfig + dynamicConfig
    }

    /**
     * Validate configuration changes before applying them
     */
    fun validateConfiguration(changes: Map<String, String>): ConfigurationValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()
        val sampleUpids = mutableMapOf<String, String>()

        try {
            // Create a temporary configuration for validation
            val currentConfig = getCurrentConfiguration()
            val testConfig = currentConfig + changes

            // Validate format type
            val formatType = testConfig[UpidConfigKeys.FORMAT_TYPE]
            if (formatType != null) {
                try {
                    UpidFormatType.valueOf(formatType.uppercase())
                } catch (e: IllegalArgumentException) {
                    errors.add("Invalid format type: $formatType. Valid types: ${UpidFormatType.values().joinToString()}")
                }
            }

            // Validate format-specific configurations
            validateFormatSpecificConfig(testConfig, errors, warnings)

            // Validate sequence configuration
            validateSequenceConfig(testConfig, errors, warnings)

            // Validate against existing patient IDs
            validateAgainstExistingPatients(testConfig, warnings)

            // Generate sample UPIDs if validation passes
            if (errors.isEmpty()) {
                sampleUpids.putAll(generateSampleUpids(testConfig))
            }

        } catch (e: Exception) {
            logger.error("Error during configuration validation", e)
            errors.add("Validation failed: ${e.message}")
        }

        return ConfigurationValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings,
            sampleUpids = sampleUpids
        )
    }

    /**
     * Apply configuration changes with validation
     */
    @Transactional
    fun applyConfiguration(request: ConfigurationChangeRequest): ConfigurationValidationResult {
        logger.info("Applying configuration changes: ${request.changes.keys}")

        // Validate first
        val validation = validateConfiguration(request.changes)
        if (!validation.isValid) {
            logger.warn("Configuration validation failed: ${validation.errors}")
            return validation
        }

        if (request.validateOnly) {
            logger.info("Validation-only request completed successfully")
            return validation
        }

        try {
            // Apply changes
            request.changes.forEach { (key, value) ->
                val oldValue = configRepo.getConfigValue(key)
                
                // Update configuration
                configRepo.upsertConfiguration(
                    configKey = key,
                    configValue = value,
                    updatedBy = request.changedBy
                )

                // Record history
                historyRepo.recordChange(
                    configKey = key,
                    oldValue = oldValue,
                    newValue = value,
                    changeReason = request.changeReason,
                    changedBy = request.changedBy
                )

                logger.info("Updated configuration: $key = $value (was: $oldValue)")
            }

            logger.info("Configuration changes applied successfully")
            return validation.copy(
                sampleUpids = generateSampleUpids(getCurrentConfiguration())
            )

        } catch (e: Exception) {
            logger.error("Failed to apply configuration changes", e)
            throw RuntimeException("Failed to apply configuration: ${e.message}", e)
        }
    }

    /**
     * Rollback configuration to previous value
     */
    @Transactional
    fun rollbackConfiguration(request: ConfigurationRollbackRequest): Boolean {
        logger.info("Rolling back configuration: ${request.configKey}")

        try {
            val currentValue = configRepo.getConfigValue(request.configKey)
            val targetValue = if (request.targetHistoryId != null) {
                historyRepo.findById(request.targetHistoryId)?.newValue
            } else {
                historyRepo.getPreviousValue(request.configKey, currentValue ?: "")
            }

            if (targetValue == null) {
                logger.warn("No rollback target found for ${request.configKey}")
                return false
            }

            // Apply rollback
            configRepo.upsertConfiguration(
                configKey = request.configKey,
                configValue = targetValue,
                updatedBy = request.rolledBackBy
            )

            // Record rollback in history
            historyRepo.recordChange(
                configKey = request.configKey,
                oldValue = currentValue,
                newValue = targetValue,
                changeReason = "ROLLBACK: ${request.rollbackReason}",
                changedBy = request.rolledBackBy
            )

            logger.info("Configuration rolled back: ${request.configKey} = $targetValue")
            return true

        } catch (e: Exception) {
            logger.error("Failed to rollback configuration", e)
            throw RuntimeException("Failed to rollback configuration: ${e.message}", e)
        }
    }

    /**
     * Get configuration history
     */
    fun getConfigurationHistory(configKey: String? = null): List<UpidConfigurationHistoryEntity> {
        return if (configKey != null) {
            historyRepo.findByConfigKey(configKey)
        } else {
            historyRepo.findRecentHistory()
        }
    }

    /**
     * Preview configuration changes
     */
    fun previewConfiguration(changes: Map<String, String>): Map<String, Any> {
        val validation = validateConfiguration(changes)
        val currentConfig = getCurrentConfiguration()
        val newConfig = currentConfig + changes

        return mapOf(
            "validation" to validation,
            "currentConfig" to currentConfig,
            "newConfig" to newConfig,
            "changedKeys" to changes.keys,
            "sampleUpids" to validation.sampleUpids
        )
    }

    // Private helper methods

    private fun getStaticConfiguration(): Map<String, String> {
        return mapOf(
            UpidConfigKeys.FORMAT_TYPE to upidConfig.format().type().name,
            UpidConfigKeys.STANDARD_FACILITY_DIGITS to upidConfig.format().standard().facilityDigits().toString(),
            UpidConfigKeys.STANDARD_NETWORK_DIGITS to upidConfig.format().standard().networkDigits().toString(),
            UpidConfigKeys.STANDARD_SEQUENCE_DIGITS to upidConfig.format().standard().sequenceDigits().toString(),
            UpidConfigKeys.STANDARD_SEPARATOR to upidConfig.format().standard().separator(),
            UpidConfigKeys.STANDARD_NETWORK_ID to upidConfig.format().standard().networkId(),
            UpidConfigKeys.CUSTOM_TEMPLATE to upidConfig.format().custom().template(),
            UpidConfigKeys.NUMERIC_PREFIX to upidConfig.format().numeric().prefix(),
            UpidConfigKeys.NUMERIC_TOTAL_DIGITS to upidConfig.format().numeric().totalDigits().toString(),
            UpidConfigKeys.ALPHANUMERIC_PREFIX to upidConfig.format().alphanumeric().prefix(),
            UpidConfigKeys.ALPHANUMERIC_FACILITY_DIGITS to upidConfig.format().alphanumeric().facilityDigits().toString(),
            UpidConfigKeys.ALPHANUMERIC_SUFFIX_PATTERN to upidConfig.format().alphanumeric().suffixPattern(),
            UpidConfigKeys.UUID_PREFIX to upidConfig.format().uuid().prefix(),
            UpidConfigKeys.UUID_INCLUDE_FACILITY to upidConfig.format().uuid().includeFacility().toString(),
            UpidConfigKeys.SEQUENCE_GLOBAL to upidConfig.sequence().global().toString(),
            UpidConfigKeys.SEQUENCE_START_FROM to upidConfig.sequence().startFrom().toString(),
            UpidConfigKeys.SEQUENCE_RESET_ANNUALLY to upidConfig.sequence().resetAnnually().toString(),
            UpidConfigKeys.SEQUENCE_RESET_MONTHLY to upidConfig.sequence().resetMonthly().toString(),
            UpidConfigKeys.VALIDATION_CHECK_DUPLICATES to upidConfig.validation().checkDuplicates().toString(),
            UpidConfigKeys.VALIDATION_ALLOW_MANUAL_OVERRIDE to upidConfig.validation().allowManualOverride().toString(),
            UpidConfigKeys.VALIDATION_RESERVED_PATTERNS to upidConfig.validation().reservedPatterns()
        )
    }

    private fun getDynamicConfiguration(): Map<String, String> {
        return configRepo.findAllActive().associate { it.configKey to it.configValue }
    }

    private fun validateFormatSpecificConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        val formatType = config[UpidConfigKeys.FORMAT_TYPE]?.let { UpidFormatType.valueOf(it.uppercase()) }

        when (formatType) {
            UpidFormatType.STANDARD -> validateStandardConfig(config, errors, warnings)
            UpidFormatType.CUSTOM -> validateCustomConfig(config, errors, warnings)
            UpidFormatType.NUMERIC -> validateNumericConfig(config, errors, warnings)
            UpidFormatType.ALPHANUMERIC -> validateAlphanumericConfig(config, errors, warnings)
            UpidFormatType.UUID -> validateUuidConfig(config, errors, warnings)
            null -> errors.add("Format type is required")
        }
    }

    private fun validateStandardConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        val facilityDigits = config[UpidConfigKeys.STANDARD_FACILITY_DIGITS]?.toIntOrNull()
        val networkDigits = config[UpidConfigKeys.STANDARD_NETWORK_DIGITS]?.toIntOrNull()
        val sequenceDigits = config[UpidConfigKeys.STANDARD_SEQUENCE_DIGITS]?.toIntOrNull()

        if (facilityDigits == null || facilityDigits < 1 || facilityDigits > 5) {
            errors.add("Facility digits must be between 1 and 5")
        }
        if (networkDigits == null || networkDigits < 1 || networkDigits > 3) {
            errors.add("Network digits must be between 1 and 3")
        }
        if (sequenceDigits == null || sequenceDigits < 4 || sequenceDigits > 12) {
            errors.add("Sequence digits must be between 4 and 12")
        }
    }

    private fun validateCustomConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        val template = config[UpidConfigKeys.CUSTOM_TEMPLATE]
        if (template.isNullOrBlank()) {
            errors.add("Custom template is required")
        } else if (!template.contains("{SEQUENCE:")) {
            errors.add("Custom template must contain a sequence placeholder like {SEQUENCE:6}")
        }
    }

    private fun validateNumericConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        val totalDigits = config[UpidConfigKeys.NUMERIC_TOTAL_DIGITS]?.toIntOrNull()
        if (totalDigits == null || totalDigits < 5 || totalDigits > 20) {
            errors.add("Total digits must be between 5 and 20")
        }
    }

    private fun validateAlphanumericConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        val facilityDigits = config[UpidConfigKeys.ALPHANUMERIC_FACILITY_DIGITS]?.toIntOrNull()
        if (facilityDigits == null || facilityDigits < 1 || facilityDigits > 5) {
            errors.add("Alphanumeric facility digits must be between 1 and 5")
        }
    }

    private fun validateUuidConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        // UUID format is generally valid, just check prefix
        val prefix = config[UpidConfigKeys.UUID_PREFIX]
        if (prefix.isNullOrBlank()) {
            warnings.add("UUID prefix is empty, UPIDs will start directly with UUID")
        }
    }

    private fun validateSequenceConfig(config: Map<String, String>, errors: MutableList<String>, warnings: MutableList<String>) {
        val startFrom = config[UpidConfigKeys.SEQUENCE_START_FROM]?.toLongOrNull()
        if (startFrom == null || startFrom < 1) {
            errors.add("Sequence start value must be a positive number")
        }
    }

    private fun validateAgainstExistingPatients(config: Map<String, String>, warnings: MutableList<String>) {
        try {
            val existingPatientCount = patientRepo.count()
            if (existingPatientCount > 0) {
                warnings.add("$existingPatientCount existing patients found. New format will only apply to new registrations.")
            }
        } catch (e: Exception) {
            logger.warn("Could not check existing patients", e)
        }
    }

    private fun generateSampleUpids(config: Map<String, String>): Map<String, String> {
        return try {
            mapOf(
                "facility1" to "Sample for facility 1",
                "facility2" to "Sample for facility 2",
                "facility10" to "Sample for facility 10"
            )
        } catch (e: Exception) {
            logger.warn("Could not generate sample UPIDs", e)
            emptyMap()
        }
    }
}
