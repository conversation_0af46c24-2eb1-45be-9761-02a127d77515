package sirobilt.meghasanjivini.common.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import org.jboss.logging.Logger
import sirobilt.meghasanjivini.common.config.UpidConfiguration
import sirobilt.meghasanjivini.common.config.UpidFormatType
import sirobilt.meghasanjivini.common.config.UpidFormatMetadata
import sirobilt.meghasanjivini.patientregistration.repository.PatientRepository
import java.time.LocalDate
import java.util.*
import java.util.concurrent.atomic.AtomicLong

/**
 * Service for generating configurable Unique Patient IDs (UPIDs)
 * Supports multiple format types: STANDARD, CUSTOM, NUMERIC, ALP<PERSON>NUMERI<PERSON>, UUID
 */
@ApplicationScoped
class UpidGeneratorService @Inject constructor(
    private val upidConfig: UpidConfiguration,
    private val patientRepo: PatientRepository
) {

    companion object {
        private val logger: Logger = Logger.getLogger(UpidGeneratorService::class.java)
        // In-memory counter for sequence generation to handle concurrent access
        private val sequenceCounter = AtomicLong(0)
        private var isInitialized = false
    }

    /**
     * Generate next UPID based on configured format
     */
    fun generateNextUpid(facilityId: String): String {
        logger.info("Generating UPID for facility: $facilityId using format: ${upidConfig.format().type()}")
        
        return when (upidConfig.format().type()) {
            UpidFormatType.STANDARD -> generateStandardFormat(facilityId)
            UpidFormatType.CUSTOM -> generateCustomFormat(facilityId)
            UpidFormatType.NUMERIC -> generateNumericFormat(facilityId)
            UpidFormatType.ALPHANUMERIC -> generateAlphanumericFormat(facilityId)
            UpidFormatType.UUID -> generateUuidFormat(facilityId)
        }
    }

    /**
     * Generate standard format: 001-00-0000-0001
     */
    private fun generateStandardFormat(facilityId: String): String {
        val config = upidConfig.format().standard()
        val facilityNumber = facilityId.toInt()
        val paddedFacilityId = facilityNumber.toString().padStart(config.facilityDigits(), '0')
        val networkId = config.networkId().padStart(config.networkDigits(), '0')
        
        val sequence = getNextSequence()
        val paddedSequence = sequence.toString().padStart(config.sequenceDigits(), '0')
        val formattedSequence = formatSequenceWithSeparator(paddedSequence, config.separator())
        
        return "$paddedFacilityId${config.separator()}$networkId${config.separator()}$formattedSequence"
    }

    /**
     * Generate custom format using template: PAT{FACILITY}{YEAR}{SEQUENCE:6}
     */
    private fun generateCustomFormat(facilityId: String): String {
        val template = upidConfig.format().custom().template()
        val sequence = getNextSequence()
        val now = LocalDate.now()
        
        var result = template
            .replace("{FACILITY}", facilityId.padStart(3, '0'))
            .replace("{NETWORK}", upidConfig.format().standard().networkId())
            .replace("{YEAR}", now.year.toString())
            .replace("{MONTH}", now.monthValue.toString().padStart(2, '0'))
            .replace("{DAY}", now.dayOfMonth.toString().padStart(2, '0'))
        
        // Handle sequence placeholders like {SEQUENCE:6}
        val sequenceRegex = "\\{SEQUENCE:(\\d+)\\}".toRegex()
        result = sequenceRegex.replace(result) { matchResult ->
            val digits = matchResult.groupValues[1].toInt()
            sequence.toString().padStart(digits, '0')
        }
        
        return result
    }

    /**
     * Generate numeric format: 1000000001
     */
    private fun generateNumericFormat(facilityId: String): String {
        val config = upidConfig.format().numeric()
        val sequence = getNextSequence()
        val prefix = config.prefix()
        val totalDigits = config.totalDigits()
        
        val numericPart = "$prefix$sequence"
        return numericPart.padStart(totalDigits, '0')
    }

    /**
     * Generate alphanumeric format: P001A0001
     */
    private fun generateAlphanumericFormat(facilityId: String): String {
        val config = upidConfig.format().alphanumeric()
        val sequence = getNextSequence()
        val prefix = config.prefix()
        val paddedFacilityId = facilityId.padStart(config.facilityDigits(), '0')
        
        var suffixPattern = config.suffixPattern()
        
        // Handle sequence placeholders in suffix pattern
        val sequenceRegex = "\\{SEQUENCE:(\\d+)\\}".toRegex()
        suffixPattern = sequenceRegex.replace(suffixPattern) { matchResult ->
            val digits = matchResult.groupValues[1].toInt()
            sequence.toString().padStart(digits, '0')
        }
        
        return "$prefix$paddedFacilityId$suffixPattern"
    }

    /**
     * Generate UUID format: PAT-550e8400-e29b-41d4-a716-************
     */
    private fun generateUuidFormat(facilityId: String): String {
        val config = upidConfig.format().uuid()
        val uuid = UUID.randomUUID().toString()
        
        return if (config.includeFacility()) {
            "${config.prefix()}F$facilityId-$uuid"
        } else {
            "${config.prefix()}$uuid"
        }
    }

    /**
     * Get next sequence number based on configuration
     */
    private fun getNextSequence(): Long {
        val sequenceConfig = upidConfig.sequence()
        
        return if (sequenceConfig.global()) {
            getGlobalSequence()
        } else {
            // For facility-specific sequences, we would need facility parameter
            // For now, defaulting to global sequence
            getGlobalSequence()
        }
    }

    /**
     * Get global sequence number using a hybrid approach:
     * 1. Initialize from database on first call
     * 2. Use in-memory atomic counter for subsequent calls
     * This ensures both persistence and thread safety
     */
    @Synchronized
    private fun getGlobalSequence(): Long {
        if (!isInitialized) {
            initializeSequenceCounter()
            isInitialized = true
        }

        val nextSequence = sequenceCounter.incrementAndGet()
        logger.debug("Generated sequence: $nextSequence")
        return nextSequence
    }

    /**
     * Initialize the sequence counter from the database
     */
    private fun initializeSequenceCounter() {
        try {
            val lastUpid = patientRepo.findLastMrnGlobally()
            val lastSequence = extractSequenceFromUpid(lastUpid)
            val startingSequence = maxOf(lastSequence, upidConfig.sequence().startFrom() - 1)

            sequenceCounter.set(startingSequence)
            logger.info("Initialized sequence counter to: $startingSequence (last UPID: $lastUpid)")
        } catch (e: Exception) {
            logger.warn("Failed to initialize sequence from database, starting from config default", e)
            sequenceCounter.set(upidConfig.sequence().startFrom() - 1)
        }
    }

    /**
     * Extract sequence number from existing UPID
     */
    private fun extractSequenceFromUpid(upid: String?): Long {
        if (upid == null) return upidConfig.sequence().startFrom() - 1
        
        return try {
            when (upidConfig.format().type()) {
                UpidFormatType.STANDARD -> {
                    // Extract from format like 001-00-0000-0001
                    val parts = upid.split("-")
                    if (parts.size >= 4) {
                        val sequencePart = parts[2] + parts[3]
                        sequencePart.toLongOrNull() ?: 0L
                    } else 0L
                }
                UpidFormatType.NUMERIC -> {
                    // Extract trailing numbers from numeric format
                    val config = upidConfig.format().numeric()
                    val prefix = config.prefix()
                    if (upid.startsWith(prefix)) {
                        upid.substring(prefix.length).toLongOrNull() ?: 0L
                    } else 0L
                }
                UpidFormatType.CUSTOM, UpidFormatType.ALPHANUMERIC -> {
                    // For custom and alphanumeric, try to extract trailing numbers
                    val numberRegex = "\\d+$".toRegex()
                    numberRegex.find(upid)?.value?.toLongOrNull() ?: 0L
                }
                UpidFormatType.UUID -> {
                    // UUID format doesn't have extractable sequence, start from config
                    upidConfig.sequence().startFrom()
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to extract sequence from UPID: $upid", e)
            upidConfig.sequence().startFrom()
        }
    }

    /**
     * Format sequence with separator for standard format
     */
    private fun formatSequenceWithSeparator(paddedSequence: String, separator: String): String {
        return if (paddedSequence.length >= 8) {
            "${paddedSequence.substring(0, 4)}$separator${paddedSequence.substring(4)}"
        } else {
            paddedSequence
        }
    }

    /**
     * Validate UPID uniqueness
     * Returns true if UPID already exists (validation failed)
     */
    fun validateUpidUniqueness(upid: String): Boolean {
        if (!upidConfig.validation().checkDuplicates()) {
            return false // Skip validation if disabled
        }
        
        return patientRepo.findByUpId(upid) != null
    }

    /**
     * Validate UPID format according to current configuration
     */
    fun validateUpidFormat(upid: String): Boolean {
        return try {
            when (upidConfig.format().type()) {
                UpidFormatType.STANDARD -> validateStandardFormat(upid)
                UpidFormatType.CUSTOM -> validateCustomFormat(upid)
                UpidFormatType.NUMERIC -> validateNumericFormat(upid)
                UpidFormatType.ALPHANUMERIC -> validateAlphanumericFormat(upid)
                UpidFormatType.UUID -> validateUuidFormat(upid)
            }
        } catch (e: Exception) {
            logger.warn("UPID format validation failed for: $upid", e)
            false
        }
    }

    private fun validateStandardFormat(upid: String): Boolean {
        val config = upidConfig.format().standard()
        val separator = config.separator()
        val pattern = "^\\d{${config.facilityDigits()}}$separator\\d{${config.networkDigits()}}$separator\\d{4}$separator\\d{4}$"
        return upid.matches(Regex(pattern))
    }

    private fun validateCustomFormat(upid: String): Boolean {
        // Custom format validation would depend on the template
        // For now, just check if it's not empty and doesn't contain reserved patterns
        return upid.isNotBlank() && !containsReservedPattern(upid)
    }

    private fun validateNumericFormat(upid: String): Boolean {
        val config = upidConfig.format().numeric()
        return upid.matches(Regex("^\\d{1,${config.totalDigits()}}$"))
    }

    private fun validateAlphanumericFormat(upid: String): Boolean {
        // Basic alphanumeric validation
        return upid.matches(Regex("^[A-Z]\\d+[A-Z]\\d+$"))
    }

    private fun validateUuidFormat(upid: String): Boolean {
        val config = upidConfig.format().uuid()
        val prefix = config.prefix()
        return upid.startsWith(prefix) && upid.length > prefix.length
    }

    private fun containsReservedPattern(upid: String): Boolean {
        val reservedPatterns = upidConfig.validation().reservedPatterns().split(",")
        return reservedPatterns.any { pattern ->
            upid.uppercase().contains(pattern.trim().uppercase())
        }
    }

    /**
     * Validate UPID (combines format and uniqueness validation)
     */
    fun validateUpid(upid: String): Boolean {
        return validateUpidFormat(upid) && !validateUpidUniqueness(upid)
    }

    /**
     * Get format information for the current configuration
     */
    fun getFormatInfo(): Map<String, Any> {
        val formatType = upidConfig.format().type()
        val metadata = UpidFormatMetadata.forType(formatType)

        return mapOf(
            "type" to formatType.name,
            "description" to metadata.description,
            "example" to metadata.example,
            "pattern" to (metadata.pattern ?: ""),
            "maxLength" to (metadata.maxLength ?: 0),
            "minLength" to (metadata.minLength ?: 0),
            "currentConfig" to getCurrentFormatConfig()
        )
    }

    /**
     * Get current format-specific configuration
     */
    private fun getCurrentFormatConfig(): Map<String, Any> {
        return when (upidConfig.format().type()) {
            UpidFormatType.STANDARD -> mapOf(
                "facilityDigits" to upidConfig.format().standard().facilityDigits(),
                "networkDigits" to upidConfig.format().standard().networkDigits(),
                "sequenceDigits" to upidConfig.format().standard().sequenceDigits(),
                "separator" to upidConfig.format().standard().separator(),
                "networkId" to upidConfig.format().standard().networkId()
            )
            UpidFormatType.CUSTOM -> mapOf(
                "template" to upidConfig.format().custom().template()
            )
            UpidFormatType.NUMERIC -> mapOf(
                "prefix" to upidConfig.format().numeric().prefix(),
                "totalDigits" to upidConfig.format().numeric().totalDigits()
            )
            UpidFormatType.ALPHANUMERIC -> mapOf(
                "prefix" to upidConfig.format().alphanumeric().prefix(),
                "facilityDigits" to upidConfig.format().alphanumeric().facilityDigits(),
                "suffixPattern" to upidConfig.format().alphanumeric().suffixPattern()
            )
            UpidFormatType.UUID -> mapOf(
                "prefix" to upidConfig.format().uuid().prefix(),
                "includeFacility" to upidConfig.format().uuid().includeFacility()
            )
        }
    }
}
