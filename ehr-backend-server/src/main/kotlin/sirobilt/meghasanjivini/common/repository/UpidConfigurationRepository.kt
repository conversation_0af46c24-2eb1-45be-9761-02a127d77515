package sirobilt.meghasanjivini.common.repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.common.model.UpidConfigurationEntity
import sirobilt.meghasanjivini.common.model.UpidConfigurationHistoryEntity

/**
 * Repository for UPID configuration persistence
 */
@ApplicationScoped
class UpidConfigurationRepository : PanacheRepositoryBase<UpidConfigurationEntity, Long> {

    /**
     * Find active configuration by key
     */
    fun findActiveByKey(configKey: String): UpidConfigurationEntity? {
        return find("configKey = ?1 AND isActive = true", configKey).firstResult()
    }

    /**
     * Find all active configurations
     */
    fun findAllActive(): List<UpidConfigurationEntity> {
        return list("isActive = true ORDER BY configKey")
    }

    /**
     * Find configurations by key pattern
     */
    fun findByKeyPattern(pattern: String): List<UpidConfigurationEntity> {
        return list("configKey LIKE ?1 AND isActive = true ORDER BY configKey", pattern)
    }

    /**
     * Update or create configuration
     */
    @Transactional
    fun upsertConfiguration(
        configKey: String,
        configValue: String,
        configType: String = "STRING",
        description: String? = null,
        updatedBy: String? = null
    ): UpidConfigurationEntity {
        val existing = findActiveByKey(configKey)
        
        return if (existing != null) {
            // Update existing
            val updated = existing.copy(
                configValue = configValue,
                configType = configType,
                description = description ?: existing.description,
                updatedAt = java.time.OffsetDateTime.now(),
                updatedBy = updatedBy
            )
            persist(updated)
            updated
        } else {
            // Create new
            val newConfig = UpidConfigurationEntity(
                configKey = configKey,
                configValue = configValue,
                configType = configType,
                description = description,
                createdBy = updatedBy,
                updatedBy = updatedBy
            )
            persist(newConfig)
            newConfig
        }
    }

    /**
     * Deactivate configuration (soft delete)
     */
    @Transactional
    fun deactivateConfiguration(configKey: String, updatedBy: String? = null): Boolean {
        return update(
            "isActive = false, updatedAt = ?1, updatedBy = ?2 WHERE configKey = ?3 AND isActive = true",
            java.time.OffsetDateTime.now(),
            updatedBy,
            configKey
        ) > 0
    }

    /**
     * Get configuration value with fallback to default
     */
    fun getConfigValue(configKey: String, defaultValue: String? = null): String? {
        return findActiveByKey(configKey)?.configValue ?: defaultValue
    }

    /**
     * Check if configuration exists
     */
    fun configExists(configKey: String): Boolean {
        return findActiveByKey(configKey) != null
    }
}

/**
 * Repository for UPID configuration history
 */
@ApplicationScoped
class UpidConfigurationHistoryRepository : PanacheRepositoryBase<UpidConfigurationHistoryEntity, Long> {

    /**
     * Find history for a specific configuration key
     */
    fun findByConfigKey(configKey: String): List<UpidConfigurationHistoryEntity> {
        return list("configKey = ?1 ORDER BY changedAt DESC", configKey)
    }

    /**
     * Find recent history entries
     */
    fun findRecentHistory(limit: Int = 50): List<UpidConfigurationHistoryEntity> {
        return find("ORDER BY changedAt DESC").page(0, limit).list()
    }

    /**
     * Find rollback candidates for a configuration key
     */
    fun findRollbackCandidates(configKey: String): List<UpidConfigurationHistoryEntity> {
        return list("configKey = ?1 AND rollbackAvailable = true ORDER BY changedAt DESC", configKey)
    }

    /**
     * Record configuration change
     */
    @Transactional
    fun recordChange(
        configKey: String,
        oldValue: String?,
        newValue: String,
        changeReason: String? = null,
        changedBy: String? = null
    ): UpidConfigurationHistoryEntity {
        val historyEntry = UpidConfigurationHistoryEntity(
            configKey = configKey,
            oldValue = oldValue,
            newValue = newValue,
            changeReason = changeReason,
            changedBy = changedBy
        )
        persist(historyEntry)
        return historyEntry
    }

    /**
     * Mark history entry as not rollback-able
     */
    @Transactional
    fun disableRollback(historyId: Long): Boolean {
        return update("rollbackAvailable = false WHERE id = ?1", historyId) > 0
    }

    /**
     * Get the previous value for rollback
     */
    fun getPreviousValue(configKey: String, currentValue: String): String? {
        return find(
            "configKey = ?1 AND newValue != ?2 AND rollbackAvailable = true ORDER BY changedAt DESC",
            configKey,
            currentValue
        ).firstResult()?.newValue
    }
}
