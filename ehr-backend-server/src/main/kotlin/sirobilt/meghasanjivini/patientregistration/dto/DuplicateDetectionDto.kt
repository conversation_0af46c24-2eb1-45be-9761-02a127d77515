package sirobilt.meghasanjivini.patientregistration.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.OffsetDateTime

/**
 * Response DTO for duplicate detection results
 */
data class DuplicateDetectionResult(
    @JsonProperty("isDuplicate")
    val isDuplicate: <PERSON><PERSON><PERSON>,
    
    @JsonProperty("confidenceLevel")
    val confidenceLevel: ConfidenceLevel,
    
    @JsonProperty("overallScore")
    val overallScore: Int,
    
    @JsonProperty("potentialDuplicates")
    val potentialDuplicates: List<PotentialDuplicate>,
    
    @JsonProperty("action")
    val action: DuplicateAction,
    
    @JsonProperty("message")
    val message: String,
    
    @JsonProperty("detectionTime")
    val detectionTime: OffsetDateTime = OffsetDateTime.now()
)

/**
 * Information about a potential duplicate patient
 */
data class PotentialDuplicate(
    @JsonProperty("patientId")
    val patientId: String,
    
    @JsonProperty("fullName")
    val fullName: String,
    
    @JsonProperty("dateOfBirth")
    val dateOfBirth: String?,
    
    @JsonProperty("phone")
    val phone: String?,
    
    @JsonProperty("email")
    val email: String?,
    
    @JsonProperty("facilityId")
    val facilityId: String,
    
    @JsonProperty("matchScore")
    val matchScore: Int,
    
    @JsonProperty("matchingCriteria")
    val matchingCriteria: List<MatchingCriterion>,
    
    @JsonProperty("registrationDate")
    val registrationDate: OffsetDateTime
)

/**
 * Details about what criteria matched
 */
data class MatchingCriterion(
    @JsonProperty("field")
    val field: String,
    
    @JsonProperty("matchType")
    val matchType: MatchType,
    
    @JsonProperty("score")
    val score: Int,
    
    @JsonProperty("weight")
    val weight: Int,
    
    @JsonProperty("originalValue")
    val originalValue: String?,
    
    @JsonProperty("matchedValue")
    val matchedValue: String?,
    
    @JsonProperty("similarity")
    val similarity: Double? = null
)

/**
 * Request DTO for duplicate resolution
 */
data class DuplicateResolutionRequest(
    @JsonProperty("candidatePatientId")
    val candidatePatientId: String,
    
    @JsonProperty("duplicatePatientId")
    val duplicatePatientId: String,
    
    @JsonProperty("action")
    val resolutionAction: ResolutionAction,
    
    @JsonProperty("notes")
    val notes: String? = null,
    
    @JsonProperty("reviewedBy")
    val reviewedBy: String
)

/**
 * Response DTO for duplicate resolution
 */
data class DuplicateResolutionResponse(
    @JsonProperty("success")
    val success: Boolean,
    
    @JsonProperty("message")
    val message: String,
    
    @JsonProperty("primaryPatientId")
    val primaryPatientId: String? = null,
    
    @JsonProperty("mergedPatientId")
    val mergedPatientId: String? = null
)

/**
 * Configuration DTO for duplicate detection settings
 */
data class DuplicateDetectionConfig(
    @JsonProperty("enabled")
    val enabled: Boolean,
    
    @JsonProperty("highThreshold")
    val highThreshold: Int,
    
    @JsonProperty("mediumThreshold")
    val mediumThreshold: Int,
    
    @JsonProperty("weights")
    val weights: MatchingWeights,
    
    @JsonProperty("fuzzyThreshold")
    val fuzzyThreshold: Int,
    
    @JsonProperty("timeoutSeconds")
    val timeoutSeconds: Int,
    
    @JsonProperty("auditEnabled")
    val auditEnabled: Boolean
)

/**
 * Matching weights configuration
 */
data class MatchingWeights(
    @JsonProperty("nameExact")
    val nameExact: Int,
    
    @JsonProperty("nameFuzzy")
    val nameFuzzy: Int,
    
    @JsonProperty("dateOfBirth")
    val dateOfBirth: Int,
    
    @JsonProperty("phone")
    val phone: Int,
    
    @JsonProperty("email")
    val email: Int,
    
    @JsonProperty("address")
    val address: Int,
    
    @JsonProperty("identifier")
    val identifier: Int
)

/**
 * Enums for duplicate detection
 */
enum class ConfidenceLevel {
    HIGH, MEDIUM, LOW
}

enum class DuplicateAction {
    BLOCK_REGISTRATION,
    FLAG_FOR_REVIEW,
    ALLOW_REGISTRATION
}

enum class MatchType {
    EXACT, FUZZY, PARTIAL, PHONETIC
}

enum class ResolutionAction {
    MERGE_RECORDS,
    MARK_AS_DIFFERENT,
    CREATE_NEW_ANYWAY
}

/**
 * Batch duplicate detection request
 */
data class BatchDuplicateDetectionRequest(
    @JsonProperty("facilityId")
    val facilityId: String? = null,
    
    @JsonProperty("dateFrom")
    val dateFrom: String? = null,
    
    @JsonProperty("dateTo")
    val dateTo: String? = null,
    
    @JsonProperty("threshold")
    val threshold: Int = 70
)

/**
 * Batch duplicate detection response
 */
data class BatchDuplicateDetectionResponse(
    @JsonProperty("totalPatientsScanned")
    val totalPatientsScanned: Int,
    
    @JsonProperty("duplicateGroupsFound")
    val duplicateGroupsFound: Int,
    
    @JsonProperty("duplicateGroups")
    val duplicateGroups: List<DuplicateGroup>,
    
    @JsonProperty("processingTime")
    val processingTime: Long
)

/**
 * Group of duplicate patients
 */
data class DuplicateGroup(
    @JsonProperty("groupId")
    val groupId: String,
    
    @JsonProperty("patients")
    val patients: List<PotentialDuplicate>,
    
    @JsonProperty("averageScore")
    val averageScore: Int,
    
    @JsonProperty("recommendedAction")
    val recommendedAction: String
)
