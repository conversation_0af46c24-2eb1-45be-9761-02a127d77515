package sirobilt.meghasanjivini.patientregistration.service

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import org.eclipse.microprofile.config.inject.ConfigProperty
import sirobilt.meghasanjivini.patientregistration.dto.DuplicateDetectionConfig
import sirobilt.meghasanjivini.patientregistration.dto.MatchingWeights
import sirobilt.meghasanjivini.patientregistration.repository.DuplicateDetectionConfigRepository
import java.util.logging.Logger

/**
 * Service for managing duplicate detection configuration
 */
@ApplicationScoped
class DuplicateDetectionConfigService {

    @Inject
    lateinit var configRepository: DuplicateDetectionConfigRepository

    @ConfigProperty(name = "duplicate.detection.enabled", defaultValue = "true")
    lateinit var defaultEnabled: String

    @ConfigProperty(name = "duplicate.detection.threshold.high", defaultValue = "85")
    lateinit var defaultHighThreshold: String

    @ConfigProperty(name = "duplicate.detection.threshold.medium", defaultValue = "70")
    lateinit var defaultMediumThreshold: String

    @ConfigProperty(name = "duplicate.detection.timeout.seconds", defaultValue = "2")
    lateinit var defaultTimeoutSeconds: String

    @ConfigProperty(name = "duplicate.detection.audit.enabled", defaultValue = "true")
    lateinit var defaultAuditEnabled: String

    private val logger: Logger = Logger.getLogger(DuplicateDetectionConfigService::class.java.name)

    /**
     * Get current duplicate detection configuration
     */
    fun getCurrentConfig(): DuplicateDetectionConfig {
        return DuplicateDetectionConfig(
            enabled = getBooleanConfig("duplicate.detection.enabled", defaultEnabled.toBoolean()),
            highThreshold = getIntConfig("duplicate.detection.threshold.high", defaultHighThreshold.toInt()),
            mediumThreshold = getIntConfig("duplicate.detection.threshold.medium", defaultMediumThreshold.toInt()),
            weights = getCurrentWeights(),
            fuzzyThreshold = getIntConfig("duplicate.detection.fuzzy.threshold", 80),
            timeoutSeconds = getIntConfig("duplicate.detection.timeout.seconds", defaultTimeoutSeconds.toInt()),
            auditEnabled = getBooleanConfig("duplicate.detection.audit.enabled", defaultAuditEnabled.toBoolean())
        )
    }

    /**
     * Get current matching weights configuration
     */
    fun getCurrentWeights(): MatchingWeights {
        return MatchingWeights(
            nameExact = getIntConfig("duplicate.detection.weight.name.exact", 40),
            nameFuzzy = getIntConfig("duplicate.detection.weight.name.fuzzy", 25),
            dateOfBirth = getIntConfig("duplicate.detection.weight.dob.exact", 30),
            phone = getIntConfig("duplicate.detection.weight.phone", 20),
            email = getIntConfig("duplicate.detection.weight.email", 15),
            address = getIntConfig("duplicate.detection.weight.address", 10),
            identifier = getIntConfig("duplicate.detection.weight.identifier", 35)
        )
    }

    /**
     * Update duplicate detection configuration
     */
    @Transactional
    fun updateConfig(config: DuplicateDetectionConfig): DuplicateDetectionConfig {
        logger.info("Updating duplicate detection configuration")

        // Validate configuration
        validateConfig(config)

        // Update individual configuration values
        updateConfigValue("duplicate.detection.enabled", config.enabled.toString())
        updateConfigValue("duplicate.detection.threshold.high", config.highThreshold.toString())
        updateConfigValue("duplicate.detection.threshold.medium", config.mediumThreshold.toString())
        updateConfigValue("duplicate.detection.fuzzy.threshold", config.fuzzyThreshold.toString())
        updateConfigValue("duplicate.detection.timeout.seconds", config.timeoutSeconds.toString())
        updateConfigValue("duplicate.detection.audit.enabled", config.auditEnabled.toString())

        // Update weights
        updateConfigValue("duplicate.detection.weight.name.exact", config.weights.nameExact.toString())
        updateConfigValue("duplicate.detection.weight.name.fuzzy", config.weights.nameFuzzy.toString())
        updateConfigValue("duplicate.detection.weight.dob.exact", config.weights.dateOfBirth.toString())
        updateConfigValue("duplicate.detection.weight.phone", config.weights.phone.toString())
        updateConfigValue("duplicate.detection.weight.email", config.weights.email.toString())
        updateConfigValue("duplicate.detection.weight.address", config.weights.address.toString())
        updateConfigValue("duplicate.detection.weight.identifier", config.weights.identifier.toString())

        logger.info("Duplicate detection configuration updated successfully")
        return getCurrentConfig()
    }

    /**
     * Reset configuration to defaults
     */
    @Transactional
    fun resetToDefaults(): DuplicateDetectionConfig {
        logger.info("Resetting duplicate detection configuration to defaults")

        // Reset to default values
        updateConfigValue("duplicate.detection.enabled", "true")
        updateConfigValue("duplicate.detection.threshold.high", "85")
        updateConfigValue("duplicate.detection.threshold.medium", "70")
        updateConfigValue("duplicate.detection.fuzzy.threshold", "80")
        updateConfigValue("duplicate.detection.timeout.seconds", "2")
        updateConfigValue("duplicate.detection.audit.enabled", "true")

        // Reset weights to defaults
        updateConfigValue("duplicate.detection.weight.name.exact", "40")
        updateConfigValue("duplicate.detection.weight.name.fuzzy", "25")
        updateConfigValue("duplicate.detection.weight.dob.exact", "30")
        updateConfigValue("duplicate.detection.weight.phone", "20")
        updateConfigValue("duplicate.detection.weight.email", "15")
        updateConfigValue("duplicate.detection.weight.address", "10")
        updateConfigValue("duplicate.detection.weight.identifier", "35")

        return getCurrentConfig()
    }

    /**
     * Get boolean configuration value
     */
    private fun getBooleanConfig(key: String, defaultValue: Boolean): Boolean {
        return try {
            configRepository.findByKey(key)?.configValue?.toBoolean() ?: defaultValue
        } catch (e: Exception) {
            logger.warning("Error reading boolean config for key: $key, using default: $defaultValue")
            defaultValue
        }
    }

    /**
     * Get integer configuration value
     */
    private fun getIntConfig(key: String, defaultValue: Int): Int {
        return try {
            configRepository.findByKey(key)?.configValue?.toInt() ?: defaultValue
        } catch (e: Exception) {
            logger.warning("Error reading int config for key: $key, using default: $defaultValue")
            defaultValue
        }
    }

    /**
     * Update configuration value
     */
    private fun updateConfigValue(key: String, value: String) {
        val existingConfig = configRepository.findByKey(key)
        if (existingConfig != null) {
            existingConfig.configValue = value
            existingConfig.updatedAt = java.time.OffsetDateTime.now()
            configRepository.persist(existingConfig)
        } else {
            // Create new config entry if it doesn't exist
            val newConfig = sirobilt.meghasanjivini.patientregistration.model.DuplicateDetectionConfigEntity(
                configKey = key,
                configValue = value,
                description = "Auto-created configuration",
                isActive = true
            )
            configRepository.persist(newConfig)
        }
    }

    /**
     * Validate configuration values
     */
    private fun validateConfig(config: DuplicateDetectionConfig) {
        // Validate thresholds
        if (config.highThreshold < 0 || config.highThreshold > 100) {
            throw IllegalArgumentException("High threshold must be between 0 and 100")
        }
        if (config.mediumThreshold < 0 || config.mediumThreshold > 100) {
            throw IllegalArgumentException("Medium threshold must be between 0 and 100")
        }
        if (config.mediumThreshold >= config.highThreshold) {
            throw IllegalArgumentException("Medium threshold must be less than high threshold")
        }

        // Validate fuzzy threshold
        if (config.fuzzyThreshold < 0 || config.fuzzyThreshold > 100) {
            throw IllegalArgumentException("Fuzzy threshold must be between 0 and 100")
        }

        // Validate timeout
        if (config.timeoutSeconds < 1 || config.timeoutSeconds > 30) {
            throw IllegalArgumentException("Timeout must be between 1 and 30 seconds")
        }

        // Validate weights (should be positive)
        val weights = config.weights
        if (weights.nameExact < 0 || weights.nameFuzzy < 0 || weights.dateOfBirth < 0 ||
            weights.phone < 0 || weights.email < 0 || weights.address < 0 || weights.identifier < 0) {
            throw IllegalArgumentException("All weights must be non-negative")
        }

        // Validate that at least some weights are positive
        val totalWeight = weights.nameExact + weights.nameFuzzy + weights.dateOfBirth +
                weights.phone + weights.email + weights.address + weights.identifier
        if (totalWeight <= 0) {
            throw IllegalArgumentException("At least one weight must be positive")
        }
    }

    /**
     * Check if duplicate detection is enabled
     */
    fun isEnabled(): Boolean {
        return getBooleanConfig("duplicate.detection.enabled", true)
    }

    /**
     * Check if audit logging is enabled
     */
    fun isAuditEnabled(): Boolean {
        return getBooleanConfig("duplicate.detection.audit.enabled", true)
    }

    /**
     * Get timeout in seconds
     */
    fun getTimeoutSeconds(): Int {
        return getIntConfig("duplicate.detection.timeout.seconds", 2)
    }

    /**
     * Get high confidence threshold
     */
    fun getHighThreshold(): Int {
        return getIntConfig("duplicate.detection.threshold.high", 85)
    }

    /**
     * Get medium confidence threshold
     */
    fun getMediumThreshold(): Int {
        return getIntConfig("duplicate.detection.threshold.medium", 70)
    }
}
