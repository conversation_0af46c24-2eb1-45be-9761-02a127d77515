package sirobilt.meghasanjivini.patientregistration.service

import org.apache.commons.text.similarity.JaroWinklerSimilarity
import org.apache.commons.text.similarity.LevenshteinDistance
import jakarta.enterprise.context.ApplicationScoped
import java.text.Normalizer
import java.util.*

/**
 * Utility class for fuzzy string matching and similarity calculations
 */
@ApplicationScoped
class FuzzyMatchingUtils {

    private val jaroWinkler = JaroWinklerSimilarity()
    private val levenshtein = LevenshteinDistance()

    /**
     * Calculate similarity between two strings using Jaro-Winkler algorithm
     * Returns a score between 0.0 (no similarity) and 1.0 (identical)
     */
    fun calculateJaroWinklerSimilarity(str1: String?, str2: String?): Double {
        if (str1.isNullOrBlank() || str2.isNullOrBlank()) return 0.0
        
        val normalized1 = normalizeString(str1)
        val normalized2 = normalizeString(str2)
        
        return jaroWinkler.apply(normalized1, normalized2)
    }

    /**
     * Calculate Levenshtein distance between two strings
     * Returns the number of single-character edits required to change one string into another
     */
    fun calculateLevenshteinDistance(str1: String?, str2: String?): Int {
        if (str1.isNullOrBlank() && str2.isNullOrBlank()) return 0
        if (str1.isNullOrBlank() || str2.isNullOrBlank()) return maxOf(str1?.length ?: 0, str2?.length ?: 0)
        
        val normalized1 = normalizeString(str1)
        val normalized2 = normalizeString(str2)
        
        return levenshtein.apply(normalized1, normalized2)
    }

    /**
     * Calculate similarity percentage based on Levenshtein distance
     * Returns a percentage between 0 and 100
     */
    fun calculateLevenshteinSimilarity(str1: String?, str2: String?): Double {
        if (str1.isNullOrBlank() && str2.isNullOrBlank()) return 100.0
        if (str1.isNullOrBlank() || str2.isNullOrBlank()) return 0.0
        
        val distance = calculateLevenshteinDistance(str1, str2)
        val maxLength = maxOf(str1.length, str2.length)
        
        return if (maxLength == 0) 100.0 else ((maxLength - distance).toDouble() / maxLength) * 100.0
    }

    /**
     * Check if two names are similar based on fuzzy matching
     * Considers various name variations and common misspellings
     */
    fun areNamesSimilar(name1: String?, name2: String?, threshold: Double = 0.8): Boolean {
        if (name1.isNullOrBlank() || name2.isNullOrBlank()) return false
        
        // Exact match after normalization
        if (normalizeString(name1) == normalizeString(name2)) return true
        
        // Jaro-Winkler similarity
        val jaroSimilarity = calculateJaroWinklerSimilarity(name1, name2)
        if (jaroSimilarity >= threshold) return true
        
        // Check individual name parts
        val parts1 = name1.trim().split("\\s+".toRegex())
        val parts2 = name2.trim().split("\\s+".toRegex())
        
        // Check if any significant parts match
        for (part1 in parts1) {
            for (part2 in parts2) {
                if (part1.length >= 3 && part2.length >= 3) {
                    val partSimilarity = calculateJaroWinklerSimilarity(part1, part2)
                    if (partSimilarity >= threshold) return true
                }
            }
        }
        
        return false
    }

    /**
     * Check if two phone numbers are similar
     * Handles different formatting and country codes
     */
    fun arePhoneNumbersSimilar(phone1: String?, phone2: String?): Boolean {
        if (phone1.isNullOrBlank() || phone2.isNullOrBlank()) return false
        
        val normalized1 = normalizePhoneNumber(phone1)
        val normalized2 = normalizePhoneNumber(phone2)
        
        // Exact match
        if (normalized1 == normalized2) return true
        
        // Check if one is a subset of the other (handling country codes)
        return normalized1.endsWith(normalized2) || normalized2.endsWith(normalized1)
    }

    /**
     * Check if two email addresses are similar
     */
    fun areEmailsSimilar(email1: String?, email2: String?): Boolean {
        if (email1.isNullOrBlank() || email2.isNullOrBlank()) return false
        
        val normalized1 = email1.lowercase().trim()
        val normalized2 = email2.lowercase().trim()
        
        return normalized1 == normalized2
    }

    /**
     * Check if two addresses are similar using fuzzy matching
     */
    fun areAddressesSimilar(address1: String?, address2: String?, threshold: Double = 0.7): Boolean {
        if (address1.isNullOrBlank() || address2.isNullOrBlank()) return false
        
        val normalized1 = normalizeAddress(address1)
        val normalized2 = normalizeAddress(address2)
        
        val similarity = calculateJaroWinklerSimilarity(normalized1, normalized2)
        return similarity >= threshold
    }

    /**
     * Calculate overall name similarity considering first, middle, and last names
     */
    fun calculateNameSimilarity(
        firstName1: String?, middleName1: String?, lastName1: String?,
        firstName2: String?, middleName2: String?, lastName2: String?
    ): Double {
        var totalSimilarity = 0.0
        var weightSum = 0.0
        
        // First name (weight: 40%)
        if (!firstName1.isNullOrBlank() && !firstName2.isNullOrBlank()) {
            totalSimilarity += calculateJaroWinklerSimilarity(firstName1, firstName2) * 0.4
            weightSum += 0.4
        }
        
        // Last name (weight: 40%)
        if (!lastName1.isNullOrBlank() && !lastName2.isNullOrBlank()) {
            totalSimilarity += calculateJaroWinklerSimilarity(lastName1, lastName2) * 0.4
            weightSum += 0.4
        }
        
        // Middle name (weight: 20%)
        if (!middleName1.isNullOrBlank() && !middleName2.isNullOrBlank()) {
            totalSimilarity += calculateJaroWinklerSimilarity(middleName1, middleName2) * 0.2
            weightSum += 0.2
        }
        
        return if (weightSum > 0) totalSimilarity / weightSum else 0.0
    }

    /**
     * Normalize string for comparison
     */
    private fun normalizeString(input: String): String {
        return Normalizer.normalize(input, Normalizer.Form.NFD)
            .replace("\\p{M}".toRegex(), "") // Remove diacritical marks
            .lowercase()
            .trim()
            .replace("\\s+".toRegex(), " ") // Normalize whitespace
    }

    /**
     * Normalize phone number for comparison
     */
    private fun normalizePhoneNumber(phone: String): String {
        return phone.replace("[^0-9]".toRegex(), "") // Keep only digits
    }

    /**
     * Normalize address for comparison
     */
    private fun normalizeAddress(address: String): String {
        return normalizeString(address)
            .replace("\\b(street|st|road|rd|avenue|ave|lane|ln|drive|dr|place|pl|court|ct)\\b".toRegex(), "")
            .replace("\\s+".toRegex(), " ")
            .trim()
    }

    /**
     * Generate phonetic representation of a name using Soundex-like algorithm
     */
    fun generatePhoneticCode(name: String?): String {
        if (name.isNullOrBlank()) return ""
        
        val normalized = normalizeString(name)
        if (normalized.isEmpty()) return ""
        
        // Simple Soundex-like implementation
        val code = StringBuilder()
        code.append(normalized[0].uppercaseChar())
        
        val consonantMap = mapOf(
            'b' to '1', 'f' to '1', 'p' to '1', 'v' to '1',
            'c' to '2', 'g' to '2', 'j' to '2', 'k' to '2', 'q' to '2', 's' to '2', 'x' to '2', 'z' to '2',
            'd' to '3', 't' to '3',
            'l' to '4',
            'm' to '5', 'n' to '5',
            'r' to '6'
        )
        
        var prevCode = consonantMap[normalized[0]] ?: '0'
        
        for (i in 1 until normalized.length) {
            val char = normalized[i]
            val currentCode = consonantMap[char] ?: '0'
            
            if (currentCode != '0' && currentCode != prevCode) {
                code.append(currentCode)
                if (code.length >= 4) break
            }
            
            if (currentCode != '0') {
                prevCode = currentCode
            }
        }
        
        // Pad with zeros if necessary
        while (code.length < 4) {
            code.append('0')
        }
        
        return code.toString()
    }
}
