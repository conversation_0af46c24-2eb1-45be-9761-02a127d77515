package sirobilt.meghasanjivini.patientregistration.service

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.model.DuplicateDetectionLogEntity
import sirobilt.meghasanjivini.patientregistration.model.Patient
import sirobilt.meghasanjivini.patientregistration.repository.DuplicateDetectionLogRepository
import sirobilt.meghasanjivini.patientregistration.repository.PatientDuplicateRepository
import java.time.OffsetDateTime
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit
import java.util.logging.Logger

/**
 * Main service for duplicate patient detection
 */
@ApplicationScoped
@Transactional
class DuplicateDetectionService {

    @Inject
    lateinit var configService: DuplicateDetectionConfigService

    @Inject
    lateinit var matchingEngine: PatientMatchingEngine

    @Inject
    lateinit var patientDuplicateRepository: PatientDuplicateRepository

    @Inject
    lateinit var logRepository: DuplicateDetectionLogRepository

    @Inject
    lateinit var objectMapper: ObjectMapper

    private val logger: Logger = Logger.getLogger(DuplicateDetectionService::class.java.name)

    /**
     * Perform duplicate detection for patient registration
     */
    @Transactional
    fun detectDuplicates(candidateData: PatientRegistrationDto): DuplicateDetectionResult {
        val startTime = System.currentTimeMillis()

        return try {
            if (!configService.isEnabled()) {
                return DuplicateDetectionResult(
                    isDuplicate = false,
                    confidenceLevel = ConfidenceLevel.LOW,
                    overallScore = 0,
                    potentialDuplicates = emptyList(),
                    action = DuplicateAction.ALLOW_REGISTRATION,
                    message = "Duplicate detection is disabled"
                )
            }

            val config = configService.getCurrentConfig()

            // 👉 Just run synchronously for now
            val result = performDuplicateDetection(candidateData, config)

            if (config.auditEnabled) {
                logDetectionEvent(candidateData, result, "REGISTRATION")
            }

            val processingTime = System.currentTimeMillis() - startTime
            logger.info("Duplicate detection completed in ${processingTime}ms for patient: ${candidateData.firstName}")

            result
        } catch (e: Exception) {
            logger.severe("Error during duplicate detection: ${e.message}")
            DuplicateDetectionResult(
                isDuplicate = false,
                confidenceLevel = ConfidenceLevel.LOW,
                overallScore = 0,
                potentialDuplicates = emptyList(),
                action = DuplicateAction.ALLOW_REGISTRATION,
                message = "Error during duplicate detection, allowing registration"
            )
        }
    }


    /**
     * Perform the actual duplicate detection logic
     */
    private fun performDuplicateDetection(
        candidateData: PatientRegistrationDto,
        config: DuplicateDetectionConfig
    ): DuplicateDetectionResult {
        val potentialDuplicates = mutableListOf<PotentialDuplicate>()
        
        // Find potential duplicates using different strategies
        val candidatePatients = findCandidatePatients(candidateData)
        
        if (candidatePatients.isEmpty()) {
            return DuplicateDetectionResult(
                isDuplicate = false,
                confidenceLevel = ConfidenceLevel.LOW,
                overallScore = 0,
                potentialDuplicates = emptyList(),
                action = DuplicateAction.ALLOW_REGISTRATION,
                message = "No potential duplicates found"
            )
        }

        var highestScore = 0
        
        // Calculate match scores for each candidate
        for (candidate in candidatePatients) {
            val matchResult = matchingEngine.calculateMatchScore(candidateData, candidate, config.weights)
            
            if (matchResult.score > 0) {
                val potentialDuplicate = PotentialDuplicate(
                    patientId = candidate.upId,
                    fullName = buildFullName(candidate.firstName, candidate.middleName, candidate.lastName),
                    dateOfBirth = candidate.dateOfBirth?.toString(),
                    phone = candidate.contacts.firstOrNull()?.phoneNumber,
                    email = candidate.contacts.firstOrNull()?.email,
                    facilityId = candidate.facilityId,
                    matchScore = matchResult.score,
                    matchingCriteria = matchResult.matchingCriteria,
                    registrationDate = candidate.registrationDate
                )
                
                potentialDuplicates.add(potentialDuplicate)
                highestScore = maxOf(highestScore, matchResult.score)
            }
        }

        // Sort by match score (highest first)
        potentialDuplicates.sortByDescending { it.matchScore }

        // Determine confidence level and action
        val confidenceLevel = when {
            highestScore >= config.highThreshold -> ConfidenceLevel.HIGH
            highestScore >= config.mediumThreshold -> ConfidenceLevel.MEDIUM
            else -> ConfidenceLevel.LOW
        }

        val action = when (confidenceLevel) {
            ConfidenceLevel.HIGH -> DuplicateAction.BLOCK_REGISTRATION
            ConfidenceLevel.MEDIUM -> DuplicateAction.FLAG_FOR_REVIEW
            ConfidenceLevel.LOW -> DuplicateAction.ALLOW_REGISTRATION
        }

        val message = when (action) {
            DuplicateAction.BLOCK_REGISTRATION -> "High confidence duplicate detected - registration blocked"
            DuplicateAction.FLAG_FOR_REVIEW -> "Potential duplicate detected - manual review required"
            DuplicateAction.ALLOW_REGISTRATION -> "Low confidence matches found - registration allowed"
        }

        return DuplicateDetectionResult(
            isDuplicate = potentialDuplicates.isNotEmpty(),
            confidenceLevel = confidenceLevel,
            overallScore = highestScore,
            potentialDuplicates = potentialDuplicates.take(10), // Limit to top 10 matches
            action = action,
            message = message
        )
    }

    /**
     * Find candidate patients for duplicate checking
     */
    private fun findCandidatePatients(candidateData: PatientRegistrationDto): List<Patient> {
        val candidates = mutableSetOf<Patient>()

        // Strategy 1: Find by name and date of birth
        val nameAndDobCandidates = patientDuplicateRepository.findPotentialDuplicatesByNameAndDob(
            candidateData.firstName,
            candidateData.lastName,
            candidateData.dateOfBirth
        )
        candidates.addAll(nameAndDobCandidates)

        // Strategy 2: Find by contact information
        val primaryContact = candidateData.contacts?.firstOrNull()
        if (primaryContact != null) {
            val contactCandidates = patientDuplicateRepository.findPotentialDuplicatesByContact(
                primaryContact.phoneNumber,
                primaryContact.email
            )
            candidates.addAll(contactCandidates)
        }

        // Strategy 3: Find by identifier
        if (!candidateData.identifierNumber.isNullOrBlank()) {
            val identifierCandidates = patientDuplicateRepository.findPotentialDuplicatesByIdentifier(
                candidateData.identifierType.name,
                candidateData.identifierNumber
            )
            candidates.addAll(identifierCandidates)
        }

        // Strategy 4: Find by ABHA number
        val abhaNumber = candidateData.abha?.abhaNumber
        if (!abhaNumber.isNullOrBlank()) {
            val abhaCandidates = patientDuplicateRepository.findPotentialDuplicatesByAbha(abhaNumber)
            candidates.addAll(abhaCandidates)
        }

        return candidates.toList()
    }

    /**
     * Log duplicate detection event
     */
    private fun logDetectionEvent(
        candidateData: PatientRegistrationDto,
        result: DuplicateDetectionResult,
        detectionType: String
    ) {
        try {
            val logEntity = DuplicateDetectionLogEntity(
                patientId = null, // Will be set after patient is created
                detectionType = detectionType,
                matchScore = result.overallScore,
                confidenceLevel = result.confidenceLevel.name,
                potentialDuplicates = if (result.potentialDuplicates.isNotEmpty()) {
                    objectMapper.writeValueAsString(result.potentialDuplicates.map { 
                        mapOf(
                            "patientId" to it.patientId,
                            "matchScore" to it.matchScore,
                            "fullName" to it.fullName
                        )
                    })
                } else null,
                matchingCriteria = if (result.potentialDuplicates.isNotEmpty()) {
                    objectMapper.writeValueAsString(result.potentialDuplicates.first().matchingCriteria)
                } else null,
                actionTaken = result.action.name,
                detectionTime = OffsetDateTime.now()
            )

            logRepository.persist(logEntity)
        } catch (e: Exception) {
            logger.warning("Failed to log duplicate detection event: ${e.message}")
        }
    }

    /**
     * Helper function to build full name
     */
    private fun buildFullName(firstName: String?, middleName: String?, lastName: String?): String {
        return listOfNotNull(firstName, middleName, lastName)
            .filter { it.isNotBlank() }
            .joinToString(" ")
    }

    /**
     * Get duplicate detection statistics
     */
    fun getDetectionStatistics(days: Int = 30): Map<String, Any> {
        val endDate = OffsetDateTime.now()
        val startDate = endDate.minusDays(days.toLong())

        val stats = logRepository.getDetectionStatistics(startDate, endDate)
        val recentHighConfidence = logRepository.findRecentHighConfidenceDuplicates(days)
        val pendingReview = logRepository.findPendingReview()

        return mapOf(
            "period_days" to days,
            "total_detections" to (stats["total"] ?: 0L),
            "high_confidence" to (stats["high_confidence"] ?: 0L),
            "medium_confidence" to (stats["medium_confidence"] ?: 0L),
            "blocked_registrations" to (stats["blocked"] ?: 0L),
            "flagged_for_review" to (stats["flagged"] ?: 0L),
            "approved_registrations" to (stats["approved"] ?: 0L),
            "pending_review_count" to pendingReview.size,
            "recent_high_confidence_count" to recentHighConfidence.size
        )
    }

    /**
     * Perform batch duplicate detection
     */
    fun performBatchDuplicateDetection(request: BatchDuplicateDetectionRequest): BatchDuplicateDetectionResponse {
        val startTime = System.currentTimeMillis()
        logger.info("Starting batch duplicate detection")

        val config = configService.getCurrentConfig()
        val duplicateGroups = mutableListOf<DuplicateGroup>()
        var totalPatientsScanned = 0
        var page = 0
        val pageSize = 100

        try {
            do {
                val patients = patientDuplicateRepository.findPatientsForBatchDetection(
                    request.facilityId,
                    request.dateFrom?.let { OffsetDateTime.parse(it) },
                    request.dateTo?.let { OffsetDateTime.parse(it) },
                    page,
                    pageSize
                )

                totalPatientsScanned += patients.size

                // Process each patient
                for (patient in patients) {
                    val candidateData = convertPatientToDto(patient)
                    val result = performDuplicateDetection(candidateData, config)

                    if (result.overallScore >= request.threshold && result.potentialDuplicates.isNotEmpty()) {
                        val groupId = "GROUP_${System.currentTimeMillis()}_${patient.upId}"
                        val duplicateGroup = DuplicateGroup(
                            groupId = groupId,
                            patients = result.potentialDuplicates,
                            averageScore = result.overallScore,
                            recommendedAction = when (result.confidenceLevel) {
                                ConfidenceLevel.HIGH -> "MERGE_RECOMMENDED"
                                ConfidenceLevel.MEDIUM -> "MANUAL_REVIEW"
                                ConfidenceLevel.LOW -> "MONITOR"
                            }
                        )
                        duplicateGroups.add(duplicateGroup)

                        // Log batch detection event
                        if (config.auditEnabled) {
                            logDetectionEvent(candidateData, result, "BATCH")
                        }
                    }
                }

                page++
            } while (patients.size == pageSize)

            val processingTime = System.currentTimeMillis() - startTime
            logger.info("Batch duplicate detection completed. Scanned: $totalPatientsScanned, Groups found: ${duplicateGroups.size}")

            return BatchDuplicateDetectionResponse(
                totalPatientsScanned = totalPatientsScanned,
                duplicateGroupsFound = duplicateGroups.size,
                duplicateGroups = duplicateGroups,
                processingTime = processingTime
            )

        } catch (e: Exception) {
            logger.severe("Error during batch duplicate detection: ${e.message}")
            throw RuntimeException("Batch duplicate detection failed", e)
        }
    }

    /**
     * Convert Patient entity to PatientRegistrationDto for duplicate detection
     */
    private fun convertPatientToDto(patient: Patient): PatientRegistrationDto {
        return PatientRegistrationDto(
            facilityId = patient.facilityId,
            identifierType = patient.identifierType,
            identifierNumber = patient.identifierNumber,
            title = patient.title,
            firstName = patient.firstName,
            middleName = patient.middleName,
            lastName = patient.lastName,
            dateOfBirth = patient.dateOfBirth,
            age = patient.age,
            gender = patient.gender,
            bloodGroup = patient.bloodGroup,
            maritalStatus = patient.maritalStatus,
            citizenship = patient.citizenship,
            religion = patient.religion,
            caste = patient.caste,
            occupation = patient.occupation,
            education = patient.education,
            annualIncome = patient.annualIncome,
            contacts = patient.contacts.map { contact ->
                ContactDto(
                    mobileNumber = contact.mobileNumber,
                    phoneNumber = contact.phoneNumber,
                    email = contact.email,
                    preferredContactMode = contact.preferredContactMode,
                    phoneContactPreference = contact.phoneContactPreference,
                    consentToShare = contact.consentToShare
                )
            },
            addresses = patient.addresses.map { address ->
                AddressDto(
                    addressType = address.addressType,
                    houseNoOrFlatNo = address.houseNoOrFlatNo,
                    localityOrSector = address.localityOrSector,
                    cityOrVillage = address.cityOrVillage,
                    pincode = address.pincode,
                    districtId = address.districtId,
                    stateId = address.stateId,
                    country = address.country
                )
            },
            abha = patient.abha?.let { abha ->
                AbhaDto(
                    abhaNumber = abha.abhaNumber,
                    abhaAddress = abha.abhaAddress
                )
            }
        )
    }
}
