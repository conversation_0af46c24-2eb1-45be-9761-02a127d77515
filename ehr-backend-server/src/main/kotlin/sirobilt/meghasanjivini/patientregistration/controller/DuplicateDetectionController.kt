package sirobilt.meghasanjivini.patientregistration.controller

import jakarta.inject.Inject
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import org.eclipse.microprofile.openapi.annotations.Operation
import org.eclipse.microprofile.openapi.annotations.tags.Tag
import sirobilt.meghasanjivini.patientregistration.dto.*
import sirobilt.meghasanjivini.patientregistration.service.DuplicateDetectionConfigService
import sirobilt.meghasanjivini.patientregistration.service.DuplicateDetectionService
import sirobilt.meghasanjivini.patientregistration.service.DuplicateResolutionService
import java.util.logging.Logger

/**
 * REST Controller for duplicate detection management
 */
@Path("/api/patients/duplicates")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Duplicate Detection", description = "Patient duplicate detection and resolution APIs")
class DuplicateDetectionController {

    @Inject
    lateinit var duplicateDetectionService: DuplicateDetectionService

    @Inject
    lateinit var duplicateResolutionService: DuplicateResolutionService

    @Inject
    lateinit var configService: DuplicateDetectionConfigService

    private val logger: Logger = Logger.getLogger(DuplicateDetectionController::class.java.name)

    /**
     * Manually check for duplicates for a given patient data
     */
    @POST
    @Path("/check")
    @Operation(summary = "Check for potential duplicates", description = "Manually check for potential duplicate patients")
    fun checkDuplicates(@Valid request: PatientRegistrationDto): Response {
        return try {
            logger.info("Manual duplicate check requested for: ${request.firstName} ${request.lastName}")
            
            val result = duplicateDetectionService.detectDuplicates(request)
            
            Response.ok(result).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid request for duplicate check: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error during manual duplicate check: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Duplicate check failed"))
                .build()
        }
    }

    /**
     * Resolve duplicate patient records
     */
    @POST
    @Path("/resolve")
    @Operation(summary = "Resolve duplicate patients", description = "Resolve duplicate patient records through merge, mark as different, or approve new")
    fun resolveDuplicate(@Valid request: DuplicateResolutionRequest): Response {
        return try {
            logger.info("Duplicate resolution requested: ${request.candidatePatientId} vs ${request.duplicatePatientId}")
            
            val result = duplicateResolutionService.resolveDuplicate(request)
            
            if (result.success) {
                Response.ok(result).build()
            } else {
                Response.status(Response.Status.BAD_REQUEST)
                    .entity(result)
                    .build()
            }
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid resolution request: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error during duplicate resolution: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Duplicate resolution failed"))
                .build()
        }
    }

    /**
     * Get pending duplicate resolutions
     */
    @GET
    @Path("/pending")
    @Operation(summary = "Get pending duplicate resolutions", description = "Get list of duplicate detections requiring manual review")
    fun getPendingResolutions(): Response {
        return try {
            val pendingResolutions = duplicateResolutionService.getPendingResolutions()
            Response.ok(pendingResolutions).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving pending resolutions: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve pending resolutions"))
                .build()
        }
    }

    /**
     * Get duplicate detection statistics
     */
    @GET
    @Path("/statistics")
    @Operation(summary = "Get duplicate detection statistics", description = "Get statistics about duplicate detection performance")
    fun getStatistics(@QueryParam("days") @DefaultValue("30") days: Int): Response {
        return try {
            val statistics = duplicateDetectionService.getDetectionStatistics(days)
            Response.ok(statistics).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving statistics: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve statistics"))
                .build()
        }
    }

    /**
     * Perform batch duplicate detection
     */
    @POST
    @Path("/batch")
    @Operation(summary = "Perform batch duplicate detection", description = "Run duplicate detection on existing patient records")
    fun performBatchDetection(@Valid request: BatchDuplicateDetectionRequest): Response {
        return try {
            logger.info("Batch duplicate detection requested")
            
            val result = duplicateDetectionService.performBatchDuplicateDetection(request)
            
            Response.ok(result).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid batch detection request: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error during batch duplicate detection: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Batch duplicate detection failed"))
                .build()
        }
    }

    /**
     * Get patient relationships (duplicates and false positives)
     */
    @GET
    @Path("/relationships/{patientId}")
    @Operation(summary = "Get patient duplicate relationships", description = "Get duplicate relationships for a specific patient")
    fun getPatientRelationships(@PathParam("patientId") patientId: String): Response {
        return try {
            val relationships = duplicateResolutionService.getPatientRelationships(patientId)
            Response.ok(relationships).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving patient relationships: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve patient relationships"))
                .build()
        }
    }
}

/**
 * REST Controller for duplicate detection configuration management
 */
@Path("/api/admin/duplicate-detection")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Duplicate Detection Admin", description = "Administrative APIs for duplicate detection configuration")
class DuplicateDetectionAdminController {

    @Inject
    lateinit var configService: DuplicateDetectionConfigService

    private val logger: Logger = Logger.getLogger(DuplicateDetectionAdminController::class.java.name)

    /**
     * Get current duplicate detection configuration
     */
    @GET
    @Path("/config")
    @Operation(summary = "Get duplicate detection configuration", description = "Get current duplicate detection settings")
    fun getConfiguration(): Response {
        return try {
            val config = configService.getCurrentConfig()
            Response.ok(config).build()
        } catch (e: Exception) {
            logger.severe("Error retrieving configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Failed to retrieve configuration"))
                .build()
        }
    }

    /**
     * Update duplicate detection configuration
     */
    @PUT
    @Path("/config")
    @Operation(summary = "Update duplicate detection configuration", description = "Update duplicate detection settings")
    fun updateConfiguration(@Valid config: DuplicateDetectionConfig): Response {
        return try {
            logger.info("Updating duplicate detection configuration")
            
            val updatedConfig = configService.updateConfig(config)
            
            Response.ok(updatedConfig).build()
        } catch (e: IllegalArgumentException) {
            logger.warning("Invalid configuration update: ${e.message}")
            Response.status(Response.Status.BAD_REQUEST)
                .entity(mapOf("error" to e.message))
                .build()
        } catch (e: Exception) {
            logger.severe("Error updating configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Configuration update failed"))
                .build()
        }
    }

    /**
     * Reset configuration to defaults
     */
    @POST
    @Path("/config/reset")
    @Operation(summary = "Reset configuration to defaults", description = "Reset duplicate detection configuration to default values")
    fun resetConfiguration(): Response {
        return try {
            logger.info("Resetting duplicate detection configuration to defaults")
            
            val defaultConfig = configService.resetToDefaults()
            
            Response.ok(defaultConfig).build()
        } catch (e: Exception) {
            logger.severe("Error resetting configuration: ${e.message}")
            Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(mapOf("error" to "Configuration reset failed"))
                .build()
        }
    }
}
