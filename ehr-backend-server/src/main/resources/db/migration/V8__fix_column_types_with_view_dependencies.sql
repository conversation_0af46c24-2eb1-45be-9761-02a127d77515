-- Migration to fix column type issues with view dependencies
-- This migration handles the PostgreSQL constraint where columns used by views cannot be altered

-- ===================================================================
-- Step 1: Store view definitions for recreation
-- ===================================================================

-- Store the patient_summary view definition
DO $$
DECLARE
    patient_summary_definition TEXT;
    recent_registrations_definition TEXT;
BEGIN
    -- Get the view definitions
    SELECT pg_get_viewdef('patient_summary', true) INTO patient_summary_definition;
    SELECT pg_get_viewdef('recent_patient_registrations', true) INTO recent_registrations_definition;
    
    -- Create a temporary table to store view definitions
    CREATE TEMP TABLE IF NOT EXISTS temp_view_definitions (
        view_name VARCHAR(100),
        view_definition TEXT
    );
    
    -- Store the definitions
    INSERT INTO temp_view_definitions VALUES 
        ('patient_summary', patient_summary_definition),
        ('recent_patient_registrations', recent_registrations_definition);
END $$;

-- ===================================================================
-- Step 2: Drop dependent views
-- ===================================================================

DROP VIEW IF EXISTS recent_patient_registrations;
DROP VIEW IF EXISTS patient_summary;

-- ===================================================================
-- Step 3: Alter column types (if needed)
-- ===================================================================

-- Note: Since the original tables were created with VARCHAR (unlimited length),
-- and Hibernate wants VARCHAR(255), we need to ensure compatibility.
-- However, PostgreSQL VARCHAR without length specification is equivalent to TEXT,
-- so we'll ensure the columns are properly typed.

-- Check and update patient_abha table columns if needed
DO $$
BEGIN
    -- Check if abha_number column needs to be altered
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_abha' 
        AND column_name = 'abha_number' 
        AND data_type = 'text'
    ) THEN
        -- Convert from TEXT to VARCHAR if it was TEXT
        ALTER TABLE patient_abha ALTER COLUMN abha_number TYPE VARCHAR;
    END IF;
    
    -- Check if patient_id column needs to be altered
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_abha' 
        AND column_name = 'patient_id' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE patient_abha ALTER COLUMN patient_id TYPE VARCHAR;
    END IF;
END $$;

-- Check and update patient_contacts table columns if needed
DO $$
BEGIN
    -- Check if email column needs to be altered
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_contacts' 
        AND column_name = 'email' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE patient_contacts ALTER COLUMN email TYPE VARCHAR;
    END IF;
    
    -- Check if mobile_number column needs to be altered
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_contacts' 
        AND column_name = 'mobile_number' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE patient_contacts ALTER COLUMN mobile_number TYPE VARCHAR;
    END IF;
    
    -- Check if patient_id column needs to be altered
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_contacts' 
        AND column_name = 'patient_id' 
        AND data_type = 'text'
    ) THEN
        ALTER TABLE patient_contacts ALTER COLUMN patient_id TYPE VARCHAR;
    END IF;
END $$;

-- ===================================================================
-- Step 4: Recreate the views
-- ===================================================================

-- Recreate patient_summary view
CREATE OR REPLACE VIEW patient_summary AS
SELECT
    p.patient_id,
    p.facility_id,
    p.first_name,
    p.middle_name,
    p.last_name,
    CONCAT_WS(' ', p.first_name, p.middle_name, p.last_name) as full_name,
    p.date_of_birth,
    p.age,
    p.gender,
    p.blood_group,
    p.registration_date,
    pc.mobile_number,
    pc.email,
    pa.abha_number,
    p.is_active,
    p.soft_deleted
FROM patients p
LEFT JOIN patient_contacts pc ON p.patient_id = pc.patient_id
LEFT JOIN patient_abha pa ON p.patient_id = pa.patient_id
WHERE p.soft_deleted = false;

-- Recreate recent_patient_registrations view
CREATE OR REPLACE VIEW recent_patient_registrations AS
SELECT
    p.patient_id,
    p.facility_id,
    CONCAT_WS(' ', p.first_name, p.middle_name, p.last_name) as full_name,
    p.age,
    p.gender,
    p.registration_date,
    pc.mobile_number,
    pc.email
FROM patients p
LEFT JOIN patient_contacts pc ON p.patient_id = pc.patient_id
WHERE p.soft_deleted = false
  AND p.registration_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY p.registration_date DESC;

-- ===================================================================
-- Step 5: Add comments for documentation
-- ===================================================================

COMMENT ON VIEW patient_summary IS 'Optimized view for patient summary with contact information - recreated after column type fixes';
COMMENT ON VIEW recent_patient_registrations IS 'View for recent patient registrations - recreated after column type fixes';

-- ===================================================================
-- Step 6: Update table statistics
-- ===================================================================

ANALYZE patients;
ANALYZE patient_contacts;
ANALYZE patient_abha;

-- ===================================================================
-- Step 7: Verify the migration
-- ===================================================================

-- Log successful completion
DO $$
BEGIN
    RAISE NOTICE 'Migration V100 completed successfully. Views recreated after handling column type dependencies.';
END $$;
