-- Migration for UPID Configuration Management
-- Creates tables for dynamic UPID configuration with history and rollback capabilities

-- Table for storing UPID configuration
CREATE TABLE upid_configuration (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value VARCHAR(500) NOT NULL,
    config_type VARCHAR(50) NOT NULL DEFAULT 'STRING',
    description VARCHAR(1000),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON>HA<PERSON>(100),
    updated_by VARCHAR(100),
    version BIGINT NOT NULL DEFAULT 0
);

-- Table for storing UPID configuration history
CREATE TABLE upid_configuration_history (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    old_value VARCHAR(500),
    new_value VARCHAR(500) NOT NULL,
    change_reason VARCHAR(1000),
    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(100),
    rollback_available BOOLEAN NOT NULL DEFAULT TRUE
);

-- Indexes for performance
CREATE INDEX idx_upid_config_key_active ON upid_configuration(config_key, is_active);
CREATE INDEX idx_upid_config_updated_at ON upid_configuration(updated_at);
CREATE INDEX idx_upid_config_history_key ON upid_configuration_history(config_key);
CREATE INDEX idx_upid_config_history_changed_at ON upid_configuration_history(changed_at);
CREATE INDEX idx_upid_config_history_rollback ON upid_configuration_history(config_key, rollback_available);

-- Insert default UPID configuration values
-- These will serve as the baseline configuration that can be overridden

INSERT INTO upid_configuration (config_key, config_value, config_type, description, created_by) VALUES
-- Format configuration
('upid.format.type', 'STANDARD', 'ENUM', 'UPID format type: STANDARD, CUSTOM, NUMERIC, ALPHANUMERIC, UUID', 'SYSTEM'),

-- Standard format configuration
('upid.format.standard.facility-digits', '3', 'INTEGER', 'Number of digits for facility ID in standard format', 'SYSTEM'),
('upid.format.standard.network-digits', '2', 'INTEGER', 'Number of digits for network ID in standard format', 'SYSTEM'),
('upid.format.standard.sequence-digits', '8', 'INTEGER', 'Number of digits for sequence in standard format', 'SYSTEM'),
('upid.format.standard.separator', '-', 'STRING', 'Separator character for standard format', 'SYSTEM'),
('upid.format.standard.network-id', '00', 'STRING', 'Default network ID for standard format', 'SYSTEM'),

-- Custom format configuration
('upid.format.custom.template', 'PAT{FACILITY}{YEAR}{SEQUENCE:6}', 'STRING', 'Template for custom format with placeholders', 'SYSTEM'),

-- Numeric format configuration
('upid.format.numeric.prefix', '1', 'STRING', 'Prefix for numeric format', 'SYSTEM'),
('upid.format.numeric.total-digits', '10', 'INTEGER', 'Total number of digits for numeric format', 'SYSTEM'),

-- Alphanumeric format configuration
('upid.format.alphanumeric.prefix', 'P', 'STRING', 'Prefix for alphanumeric format', 'SYSTEM'),
('upid.format.alphanumeric.facility-digits', '3', 'INTEGER', 'Number of facility digits for alphanumeric format', 'SYSTEM'),
('upid.format.alphanumeric.suffix-pattern', 'A{SEQUENCE:4}', 'STRING', 'Suffix pattern for alphanumeric format', 'SYSTEM'),

-- UUID format configuration
('upid.format.uuid.prefix', 'PAT-', 'STRING', 'Prefix for UUID format', 'SYSTEM'),
('upid.format.uuid.include-facility', 'true', 'BOOLEAN', 'Whether to include facility ID in UUID format', 'SYSTEM'),

-- Sequence management configuration
('upid.sequence.global', 'true', 'BOOLEAN', 'Whether to use global sequence across all facilities', 'SYSTEM'),
('upid.sequence.start-from', '1', 'LONG', 'Starting number for sequence generation', 'SYSTEM'),
('upid.sequence.reset-annually', 'false', 'BOOLEAN', 'Whether to reset sequence annually', 'SYSTEM'),
('upid.sequence.reset-monthly', 'false', 'BOOLEAN', 'Whether to reset sequence monthly', 'SYSTEM'),

-- Validation configuration
('upid.validation.check-duplicates', 'true', 'BOOLEAN', 'Whether to check for duplicate UPIDs', 'SYSTEM'),
('upid.validation.allow-manual-override', 'false', 'BOOLEAN', 'Whether to allow manual UPID override', 'SYSTEM'),
('upid.validation.reserved-patterns', 'ADMIN,TEST,DEMO', 'STRING', 'Comma-separated list of reserved patterns', 'SYSTEM');

-- Record initial configuration in history
INSERT INTO upid_configuration_history (config_key, old_value, new_value, change_reason, changed_by)
SELECT 
    config_key, 
    NULL, 
    config_value, 
    'Initial system configuration', 
    'SYSTEM'
FROM upid_configuration;

-- Comments for documentation
COMMENT ON TABLE upid_configuration IS 'Stores dynamic UPID configuration that can be modified at runtime';
COMMENT ON TABLE upid_configuration_history IS 'Stores history of UPID configuration changes for audit and rollback';

COMMENT ON COLUMN upid_configuration.config_key IS 'Unique configuration key (e.g., upid.format.type)';
COMMENT ON COLUMN upid_configuration.config_value IS 'Configuration value as string';
COMMENT ON COLUMN upid_configuration.config_type IS 'Data type hint for the configuration value';
COMMENT ON COLUMN upid_configuration.is_active IS 'Whether this configuration is currently active';
COMMENT ON COLUMN upid_configuration.version IS 'Optimistic locking version';

COMMENT ON COLUMN upid_configuration_history.rollback_available IS 'Whether this history entry can be used for rollback';
